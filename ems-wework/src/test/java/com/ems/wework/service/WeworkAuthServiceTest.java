package com.ems.wework.service;

import com.ems.common.core.domain.entity.SysUser;
import com.ems.framework.web.service.TokenService;
import com.ems.system.service.ISysUserService;
import com.ems.wework.config.WeworkConfig;
import com.ems.wework.domain.dto.WeworkUser;
import com.ems.wework.service.impl.WeworkAuthServiceImpl;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * 企业微信认证服务测试类
 * 
 * <AUTHOR>
 */
@ExtendWith(MockitoExtension.class)
public class WeworkAuthServiceTest {
    
    @Mock
    private WeworkConfig weworkConfig;
    
    @Mock
    private IWeworkApiService apiService;
    
    @Mock
    private ISysUserService userService;
    
    @Mock
    private TokenService tokenService;
    
    @InjectMocks
    private WeworkAuthServiceImpl authService;
    
    private WeworkUser mockWeworkUser;
    private SysUser mockSysUser;
    
    @BeforeEach
    public void setUp() {
        // 初始化模拟数据
        mockWeworkUser = new WeworkUser();
        mockWeworkUser.setUserId("test_user_id");
        mockWeworkUser.setName("测试用户");
        mockWeworkUser.setMobile("13800138000");
        mockWeworkUser.setEmail("<EMAIL>");
        mockWeworkUser.setAvatar("http://example.com/avatar.jpg");
        mockWeworkUser.setStatus(1); // 已激活
        mockWeworkUser.setEnable(1); // 启用
        mockWeworkUser.setDepartment(Arrays.asList(1, 2));
        
        mockSysUser = new SysUser();
        mockSysUser.setUserId(1L);
        mockSysUser.setUserName("testuser");
        mockSysUser.setNickName("测试用户");
        mockSysUser.setEmail("<EMAIL>");
        mockSysUser.setPhonenumber("13800138000");
        mockSysUser.setWeworkUserid("test_user_id");
        mockSysUser.setDeptId(100L);
    }
    
    @Test
    public void testBuildAuthUrl() {
        // 模拟配置
        WeworkConfig.OAuth2 oauth2Config = new WeworkConfig.OAuth2();
        oauth2Config.setEnabled(true);
        oauth2Config.setScope("snsapi_base");
        
        when(weworkConfig.getCorpId()).thenReturn("test_corp_id");
        when(weworkConfig.getAgentId()).thenReturn("test_agent_id");
        when(weworkConfig.getOauth2()).thenReturn(oauth2Config);
        
        // 测试构建授权URL
        String redirectUri = "http://example.com/callback";
        String state = "test_state";
        
        String authUrl = authService.buildAuthUrl(redirectUri, state);
        
        assertNotNull(authUrl);
        assertTrue(authUrl.contains("test_corp_id"));
        assertTrue(authUrl.contains("test_agent_id"));
        assertTrue(authUrl.contains("snsapi_base"));
        assertTrue(authUrl.contains(redirectUri));
        assertTrue(authUrl.contains(state));
    }
    
    @Test
    public void testBuildAuthUrlWithoutState() {
        // 模拟配置
        WeworkConfig.OAuth2 oauth2Config = new WeworkConfig.OAuth2();
        oauth2Config.setEnabled(true);
        oauth2Config.setScope("snsapi_base");
        
        when(weworkConfig.getCorpId()).thenReturn("test_corp_id");
        when(weworkConfig.getAgentId()).thenReturn("test_agent_id");
        when(weworkConfig.getOauth2()).thenReturn(oauth2Config);
        
        // 测试不带state参数的授权URL
        String redirectUri = "http://example.com/callback";
        
        String authUrl = authService.buildAuthUrl(redirectUri);
        
        assertNotNull(authUrl);
        assertTrue(authUrl.contains("test_corp_id"));
        assertTrue(authUrl.contains("test_agent_id"));
        assertTrue(authUrl.contains(redirectUri));
    }
    
    @Test
    public void testIsValidWeworkUser() {
        // 测试有效用户
        assertTrue(authService.isValidWeworkUser(mockWeworkUser));
        
        // 测试null用户
        assertFalse(authService.isValidWeworkUser(null));
        
        // 测试空用户ID
        WeworkUser invalidUser1 = new WeworkUser();
        invalidUser1.setUserId("");
        assertFalse(authService.isValidWeworkUser(invalidUser1));
        
        // 测试无效状态
        WeworkUser invalidUser2 = new WeworkUser();
        invalidUser2.setUserId("test_id");
        invalidUser2.setStatus(2); // 已禁用
        invalidUser2.setEnable(1);
        assertFalse(authService.isValidWeworkUser(invalidUser2));
        
        // 测试未启用
        WeworkUser invalidUser3 = new WeworkUser();
        invalidUser3.setUserId("test_id");
        invalidUser3.setStatus(1);
        invalidUser3.setEnable(0); // 禁用
        assertFalse(authService.isValidWeworkUser(invalidUser3));
    }
    
    @Test
    public void testBindWeworkUser() {
        // 模拟用户服务
        when(userService.selectUserById(1L)).thenReturn(mockSysUser);
        when(userService.selectUserByWeworkId("test_user_id")).thenReturn(null);
        when(userService.updateUser(any(SysUser.class))).thenReturn(1);
        
        // 测试绑定
        boolean result = authService.bindWeworkUser(1L, mockWeworkUser);
        
        assertTrue(result);
        verify(userService).updateUser(any(SysUser.class));
    }
    
    @Test
    public void testBindWeworkUserAlreadyBound() {
        // 模拟企业微信用户已被其他用户绑定
        SysUser existUser = new SysUser();
        existUser.setUserId(2L); // 不同的用户ID
        
        when(userService.selectUserById(1L)).thenReturn(mockSysUser);
        when(userService.selectUserByWeworkId("test_user_id")).thenReturn(existUser);
        
        // 测试绑定已被绑定的企业微信用户
        boolean result = authService.bindWeworkUser(1L, mockWeworkUser);
        
        assertFalse(result);
        verify(userService, never()).updateUser(any(SysUser.class));
    }
    
    @Test
    public void testUnbindWeworkUser() {
        // 模拟用户服务
        when(userService.selectUserById(1L)).thenReturn(mockSysUser);
        when(userService.updateUser(any(SysUser.class))).thenReturn(1);
        
        // 测试解绑
        boolean result = authService.unbindWeworkUser(1L);
        
        assertTrue(result);
        verify(userService).updateUser(argThat(user -> 
            user.getWeworkUserid() == null &&
            user.getWeworkMobile() == null &&
            user.getWeworkAvatar() == null &&
            user.getWeworkName() == null &&
            user.getWeworkDepartment() == null &&
            user.getWeworkBindTime() == null
        ));
    }
    
    @Test
    public void testGetUserByWeworkId() {
        // 模拟用户服务
        when(userService.selectUserByWeworkId("test_user_id")).thenReturn(mockSysUser);
        
        // 测试根据企业微信用户ID查询用户
        SysUser result = authService.getUserByWeworkId("test_user_id");
        
        assertNotNull(result);
        assertEquals(mockSysUser.getUserId(), result.getUserId());
        assertEquals(mockSysUser.getWeworkUserid(), result.getWeworkUserid());
    }
    
    @Test
    public void testGetUserByWeworkIdNotFound() {
        // 模拟用户不存在
        when(userService.selectUserByWeworkId("nonexistent_id")).thenReturn(null);
        
        // 测试查询不存在的用户
        SysUser result = authService.getUserByWeworkId("nonexistent_id");
        
        assertNull(result);
    }
    
    @Test
    public void testSyncWeworkUser() {
        // 模拟用户存在且信息有变化
        SysUser existUser = new SysUser();
        existUser.setUserId(1L);
        existUser.setWeworkUserid("test_user_id");
        existUser.setWeworkMobile("13800138001"); // 不同的手机号
        existUser.setWeworkName("旧名称"); // 不同的姓名
        
        when(userService.selectUserByWeworkId("test_user_id")).thenReturn(existUser);
        when(userService.updateUser(any(SysUser.class))).thenReturn(1);
        
        // 测试同步用户信息
        boolean result = authService.syncWeworkUser(mockWeworkUser);
        
        assertTrue(result);
        verify(userService).updateUser(any(SysUser.class));
    }
    
    @Test
    public void testSyncWeworkUserNoChanges() {
        // 模拟用户存在且信息无变化
        SysUser existUser = new SysUser();
        existUser.setUserId(1L);
        existUser.setWeworkUserid("test_user_id");
        existUser.setWeworkMobile("13800138000"); // 相同的手机号
        existUser.setWeworkName("测试用户"); // 相同的姓名
        existUser.setWeworkAvatar("http://example.com/avatar.jpg"); // 相同的头像
        existUser.setWeworkDepartment("[1, 2]"); // 相同的部门
        
        when(userService.selectUserByWeworkId("test_user_id")).thenReturn(existUser);
        
        // 测试同步无变化的用户信息
        boolean result = authService.syncWeworkUser(mockWeworkUser);
        
        assertTrue(result);
        verify(userService, never()).updateUser(any(SysUser.class));
    }
    
    @Test
    public void testIsWeworkLoginEnabled() {
        // 模拟登录启用
        WeworkConfig.OAuth2 oauth2Config = new WeworkConfig.OAuth2();
        oauth2Config.setEnabled(true);
        when(weworkConfig.getOauth2()).thenReturn(oauth2Config);
        
        assertTrue(authService.isWeworkLoginEnabled());
        
        // 模拟登录禁用
        oauth2Config.setEnabled(false);
        assertFalse(authService.isWeworkLoginEnabled());
    }
}
