package com.ems.wework.utils;

import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import static org.junit.jupiter.api.Assertions.*;

/**
 * 企业微信官方加解密类测试
 * 
 * <AUTHOR>
 */
public class WXBizMsgCryptTest {
    
    private static final String TEST_CORP_ID = "test_corp_id";
    private static final String TEST_AES_KEY = "1234567890123456789012345678901234567890123";
    private static final String TEST_TOKEN = "test_token";
    private static final String TEST_MESSAGE = "Hello, WeWork!";
    
    private WXBizMsgCrypt wxBizMsgCrypt;
    
    @BeforeEach
    public void setUp() {
        wxBizMsgCrypt = new WXBizMsgCrypt(TEST_TOKEN, TEST_AES_KEY, TEST_CORP_ID);
    }
    
    @Test
    public void testConstructor() {
        // 测试正常构造
        assertNotNull(wxBizMsgCrypt);
        
        // 测试无效AES密钥长度
        assertThrows(RuntimeException.class, () -> {
            new WXBizMsgCrypt(TEST_TOKEN, "invalid_key", TEST_CORP_ID);
        });
    }
    
    @Test
    public void testGetRandomStr() {
        String randomStr1 = wxBizMsgCrypt.getRandomStr();
        String randomStr2 = wxBizMsgCrypt.getRandomStr();
        
        assertNotNull(randomStr1);
        assertNotNull(randomStr2);
        assertEquals(16, randomStr1.length());
        assertEquals(16, randomStr2.length());
        assertNotEquals(randomStr1, randomStr2);
    }
    
    @Test
    public void testNetworkBytesOrder() {
        int testNumber = 12345;
        
        byte[] networkBytes = wxBizMsgCrypt.getNetworkBytesOrder(testNumber);
        int recoveredNumber = wxBizMsgCrypt.recoverNetworkBytesOrder(networkBytes);
        
        assertEquals(testNumber, recoveredNumber);
    }
    
    @Test
    public void testEncryptAndDecrypt() {
        try {
            String randomStr = wxBizMsgCrypt.getRandomStr();
            
            // 加密
            String encrypted = wxBizMsgCrypt.encrypt(randomStr, TEST_MESSAGE);
            assertNotNull(encrypted);
            assertNotEquals(TEST_MESSAGE, encrypted);
            
            // 解密
            String decrypted = wxBizMsgCrypt.decrypt(encrypted);
            assertNotNull(decrypted);
            assertEquals(TEST_MESSAGE, decrypted);
            
        } catch (Exception e) {
            fail("加解密测试失败: " + e.getMessage());
        }
    }
    
    @Test
    public void testVerifyURL() {
        try {
            String timestamp = "1234567890";
            String nonce = "test_nonce";
            String echoStr = "test_echo_str";
            
            // 首先加密echoStr
            String encryptedEchoStr = wxBizMsgCrypt.encrypt(wxBizMsgCrypt.getRandomStr(), echoStr);
            
            // 生成签名
            String signature = WeworkCryptoUtils.generateSignature(TEST_TOKEN, timestamp, nonce, encryptedEchoStr);
            
            // 验证URL
            String result = wxBizMsgCrypt.VerifyURL(signature, timestamp, nonce, encryptedEchoStr);
            
            assertEquals(echoStr, result);
            
        } catch (Exception e) {
            fail("URL验证测试失败: " + e.getMessage());
        }
    }
    
    @Test
    public void testEncryptMsg() {
        try {
            String timestamp = String.valueOf(System.currentTimeMillis());
            String nonce = "test_nonce";
            String replyMsg = "<xml><Content><![CDATA[Hello World]]></Content></xml>";
            
            String encryptedXml = wxBizMsgCrypt.EncryptMsg(replyMsg, timestamp, nonce);
            
            assertNotNull(encryptedXml);
            assertTrue(encryptedXml.contains("<Encrypt>"));
            assertTrue(encryptedXml.contains("<MsgSignature>"));
            assertTrue(encryptedXml.contains("<TimeStamp>"));
            assertTrue(encryptedXml.contains("<Nonce>"));
            
        } catch (Exception e) {
            fail("消息加密测试失败: " + e.getMessage());
        }
    }
    
    @Test
    public void testDecryptMsg() {
        try {
            String timestamp = String.valueOf(System.currentTimeMillis());
            String nonce = "test_nonce";
            String originalMsg = "<xml><Content><![CDATA[Hello World]]></Content></xml>";
            
            // 先加密消息
            String encryptedXml = wxBizMsgCrypt.EncryptMsg(originalMsg, timestamp, nonce);
            
            // 提取签名和加密数据
            String encrypt = WeworkXmlUtils.extractEncryptData(encryptedXml);
            String msgSignature = extractMsgSignature(encryptedXml);
            
            // 解密消息
            String decryptedMsg = wxBizMsgCrypt.DecryptMsg(msgSignature, timestamp, nonce, encryptedXml);
            
            assertEquals(originalMsg, decryptedMsg);
            
        } catch (Exception e) {
            fail("消息解密测试失败: " + e.getMessage());
        }
    }
    
    @Test
    public void testDecryptMsgWithInvalidSignature() {
        try {
            String timestamp = String.valueOf(System.currentTimeMillis());
            String nonce = "test_nonce";
            String originalMsg = "<xml><Content><![CDATA[Hello World]]></Content></xml>";
            
            // 先加密消息
            String encryptedXml = wxBizMsgCrypt.EncryptMsg(originalMsg, timestamp, nonce);
            
            // 使用错误的签名
            String wrongSignature = "wrong_signature";
            
            // 解密消息应该失败
            assertThrows(RuntimeException.class, () -> {
                wxBizMsgCrypt.DecryptMsg(wrongSignature, timestamp, nonce, encryptedXml);
            });
            
        } catch (Exception e) {
            fail("无效签名测试失败: " + e.getMessage());
        }
    }
    
    @Test
    public void testDecryptMsgWithWrongCorpId() {
        try {
            String timestamp = String.valueOf(System.currentTimeMillis());
            String nonce = "test_nonce";
            String originalMsg = "<xml><Content><![CDATA[Hello World]]></Content></xml>";
            
            // 使用不同的企业ID创建加解密实例
            WXBizMsgCrypt wrongCrypt = new WXBizMsgCrypt(TEST_TOKEN, TEST_AES_KEY, "wrong_corp_id");
            
            // 用原实例加密
            String encryptedXml = wxBizMsgCrypt.EncryptMsg(originalMsg, timestamp, nonce);
            
            // 提取签名
            String msgSignature = extractMsgSignature(encryptedXml);
            
            // 用错误企业ID的实例解密应该失败
            assertThrows(RuntimeException.class, () -> {
                wrongCrypt.DecryptMsg(msgSignature, timestamp, nonce, encryptedXml);
            });
            
        } catch (Exception e) {
            fail("错误企业ID测试失败: " + e.getMessage());
        }
    }
    
    @Test
    public void testEmptyMessage() {
        try {
            String emptyMessage = "";
            String randomStr = wxBizMsgCrypt.getRandomStr();
            
            String encrypted = wxBizMsgCrypt.encrypt(randomStr, emptyMessage);
            String decrypted = wxBizMsgCrypt.decrypt(encrypted);
            
            assertEquals(emptyMessage, decrypted);
            
        } catch (Exception e) {
            fail("空消息测试失败: " + e.getMessage());
        }
    }
    
    @Test
    public void testLongMessage() {
        try {
            StringBuilder longMessage = new StringBuilder();
            for (int i = 0; i < 1000; i++) {
                longMessage.append("这是一条很长的测试消息，用于测试加解密功能的稳定性。");
            }
            
            String message = longMessage.toString();
            String randomStr = wxBizMsgCrypt.getRandomStr();
            
            String encrypted = wxBizMsgCrypt.encrypt(randomStr, message);
            String decrypted = wxBizMsgCrypt.decrypt(encrypted);
            
            assertEquals(message, decrypted);
            
        } catch (Exception e) {
            fail("长消息测试失败: " + e.getMessage());
        }
    }
    
    @Test
    public void testSpecialCharacters() {
        try {
            String specialMessage = "特殊字符测试: !@#$%^&*()_+{}|:<>?[]\\;'\",./ 中文测试 🎉🎊";
            String randomStr = wxBizMsgCrypt.getRandomStr();
            
            String encrypted = wxBizMsgCrypt.encrypt(randomStr, specialMessage);
            String decrypted = wxBizMsgCrypt.decrypt(encrypted);
            
            assertEquals(specialMessage, decrypted);
            
        } catch (Exception e) {
            fail("特殊字符测试失败: " + e.getMessage());
        }
    }
    
    /**
     * 从加密XML中提取MsgSignature
     */
    private String extractMsgSignature(String encryptedXml) {
        try {
            int start = encryptedXml.indexOf("<MsgSignature><![CDATA[") + 23;
            int end = encryptedXml.indexOf("]]></MsgSignature>");
            return encryptedXml.substring(start, end);
        } catch (Exception e) {
            return null;
        }
    }
}
