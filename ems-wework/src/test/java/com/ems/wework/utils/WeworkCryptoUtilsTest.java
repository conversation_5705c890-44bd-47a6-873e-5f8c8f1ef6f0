package com.ems.wework.utils;

import org.junit.jupiter.api.Test;
import static org.junit.jupiter.api.Assertions.*;

/**
 * 企业微信加解密工具测试类
 * 
 * <AUTHOR>
 */
public class WeworkCryptoUtilsTest {
    
    private static final String TEST_CORP_ID = "test_corp_id";
    private static final String TEST_AES_KEY = "1234567890123456789012345678901234567890123";
    private static final String TEST_TOKEN = "test_token";
    private static final String TEST_MESSAGE = "Hello, WeWork!";
    
    @Test
    public void testGenerateRandomString() {
        // 测试生成随机字符串
        String randomStr1 = WeworkCryptoUtils.generateRandomString(16);
        String randomStr2 = WeworkCryptoUtils.generateRandomString(16);
        String randomStr3 = WeworkCryptoUtils.generateRandomString(); // 默认16位

        assertNotNull(randomStr1);
        assertNotNull(randomStr2);
        assertNotNull(randomStr3);
        assertEquals(16, randomStr1.length());
        assertEquals(16, randomStr2.length());
        assertEquals(16, randomStr3.length());
        assertNotEquals(randomStr1, randomStr2); // 两次生成的随机字符串应该不同
    }
    
    @Test
    public void testGenerateSignature() {
        // 测试签名生成
        String timestamp = "1234567890";
        String nonce = "test_nonce";
        String encrypt = "test_encrypt_data";
        
        String signature1 = WeworkCryptoUtils.generateSignature(TEST_TOKEN, timestamp, nonce, encrypt);
        String signature2 = WeworkCryptoUtils.generateSignature(TEST_TOKEN, timestamp, nonce, encrypt);
        
        assertNotNull(signature1);
        assertNotNull(signature2);
        assertEquals(signature1, signature2); // 相同参数应该生成相同签名
        assertEquals(40, signature1.length()); // SHA1签名长度为40
    }
    
    @Test
    public void testVerifySignature() {
        // 测试签名验证
        String timestamp = "1234567890";
        String nonce = "test_nonce";
        String encrypt = "test_encrypt_data";
        
        String signature = WeworkCryptoUtils.generateSignature(TEST_TOKEN, timestamp, nonce, encrypt);
        
        // 验证正确签名
        assertTrue(WeworkCryptoUtils.verifySignature(signature, TEST_TOKEN, timestamp, nonce, encrypt));
        
        // 验证错误签名
        assertFalse(WeworkCryptoUtils.verifySignature("wrong_signature", TEST_TOKEN, timestamp, nonce, encrypt));
        
        // 验证错误token
        assertFalse(WeworkCryptoUtils.verifySignature(signature, "wrong_token", timestamp, nonce, encrypt));
    }
    
    @Test
    public void testEncryptAndDecrypt() {
        // 测试加解密
        try {
            // 加密
            String encrypted = WeworkCryptoUtils.encrypt(TEST_MESSAGE, TEST_CORP_ID, TEST_AES_KEY);
            assertNotNull(encrypted);
            assertNotEquals(TEST_MESSAGE, encrypted);
            
            // 解密
            String decrypted = WeworkCryptoUtils.decrypt(encrypted, TEST_CORP_ID, TEST_AES_KEY);
            assertNotNull(decrypted);
            assertEquals(TEST_MESSAGE, decrypted);
            
        } catch (Exception e) {
            fail("加解密测试失败: " + e.getMessage());
        }
    }
    
    @Test
    public void testEncryptDecryptWithDifferentCorpId() {
        // 测试不同企业ID的加解密
        try {
            String encrypted = WeworkCryptoUtils.encrypt(TEST_MESSAGE, TEST_CORP_ID, TEST_AES_KEY);
            
            // 使用错误的企业ID解密应该失败
            assertThrows(RuntimeException.class, () -> {
                WeworkCryptoUtils.decrypt(encrypted, "wrong_corp_id", TEST_AES_KEY);
            });
            
        } catch (Exception e) {
            fail("企业ID验证测试失败: " + e.getMessage());
        }
    }
    
    @Test
    public void testEncryptDecryptWithEmptyMessage() {
        // 测试空消息加解密
        try {
            String emptyMessage = "";
            String encrypted = WeworkCryptoUtils.encrypt(emptyMessage, TEST_CORP_ID, TEST_AES_KEY);
            String decrypted = WeworkCryptoUtils.decrypt(encrypted, TEST_CORP_ID, TEST_AES_KEY);
            
            assertEquals(emptyMessage, decrypted);
            
        } catch (Exception e) {
            fail("空消息加解密测试失败: " + e.getMessage());
        }
    }
    
    @Test
    public void testEncryptDecryptWithLongMessage() {
        // 测试长消息加解密
        try {
            StringBuilder longMessage = new StringBuilder();
            for (int i = 0; i < 1000; i++) {
                longMessage.append("这是一条很长的测试消息，用于测试加解密功能的稳定性。");
            }
            
            String message = longMessage.toString();
            String encrypted = WeworkCryptoUtils.encrypt(message, TEST_CORP_ID, TEST_AES_KEY);
            String decrypted = WeworkCryptoUtils.decrypt(encrypted, TEST_CORP_ID, TEST_AES_KEY);
            
            assertEquals(message, decrypted);
            
        } catch (Exception e) {
            fail("长消息加解密测试失败: " + e.getMessage());
        }
    }
    
    @Test
    public void testEncryptDecryptWithSpecialCharacters() {
        // 测试特殊字符加解密
        try {
            String specialMessage = "特殊字符测试: !@#$%^&*()_+{}|:<>?[]\\;'\",./ 中文测试 🎉🎊";
            String encrypted = WeworkCryptoUtils.encrypt(specialMessage, TEST_CORP_ID, TEST_AES_KEY);
            String decrypted = WeworkCryptoUtils.decrypt(encrypted, TEST_CORP_ID, TEST_AES_KEY);
            
            assertEquals(specialMessage, decrypted);
            
        } catch (Exception e) {
            fail("特殊字符加解密测试失败: " + e.getMessage());
        }
    }
    
    @Test
    public void testInvalidAesKey() {
        // 测试无效的AES密钥
        assertThrows(RuntimeException.class, () -> {
            WeworkCryptoUtils.encrypt(TEST_MESSAGE, TEST_CORP_ID, "invalid_aes_key");
        });
        
        assertThrows(RuntimeException.class, () -> {
            WeworkCryptoUtils.decrypt("invalid_encrypted_data", TEST_CORP_ID, TEST_AES_KEY);
        });
    }
    
    @Test
    public void testNullParameters() {
        // 测试空参数
        assertThrows(RuntimeException.class, () -> {
            WeworkCryptoUtils.encrypt(null, TEST_CORP_ID, TEST_AES_KEY);
        });
        
        assertThrows(RuntimeException.class, () -> {
            WeworkCryptoUtils.encrypt(TEST_MESSAGE, null, TEST_AES_KEY);
        });
        
        assertThrows(RuntimeException.class, () -> {
            WeworkCryptoUtils.encrypt(TEST_MESSAGE, TEST_CORP_ID, null);
        });
        
        assertThrows(RuntimeException.class, () -> {
            WeworkCryptoUtils.decrypt(null, TEST_CORP_ID, TEST_AES_KEY);
        });
    }
}
