package com.ems.wework.utils;

import org.junit.jupiter.api.Test;
import static org.junit.jupiter.api.Assertions.*;

import java.util.HashMap;
import java.util.Map;

/**
 * 企业微信XML工具测试类
 * 
 * <AUTHOR>
 */
public class WeworkXmlUtilsTest {
    
    @Test
    public void testParseXmlToMap() {
        // 测试XML解析为Map
        String xmlString = "<xml>" +
                "<ToUserName><![CDATA[toUser]]></ToUserName>" +
                "<FromUserName><![CDATA[fromUser]]></FromUserName>" +
                "<CreateTime>123456789</CreateTime>" +
                "<MsgType><![CDATA[text]]></MsgType>" +
                "<Content><![CDATA[Hello World]]></Content>" +
                "</xml>";
        
        Map<String, String> result = WeworkXmlUtils.parseXmlToMap(xmlString);
        
        assertNotNull(result);
        assertEquals("toUser", result.get("ToUserName"));
        assertEquals("fromUser", result.get("FromUserName"));
        assertEquals("123456789", result.get("CreateTime"));
        assertEquals("text", result.get("MsgType"));
        assertEquals("Hello World", result.get("Content"));
    }
    
    @Test
    public void testParseXmlToMapWithEmptyValues() {
        // 测试包含空值的XML解析
        String xmlString = "<xml>" +
                "<ToUserName><![CDATA[toUser]]></ToUserName>" +
                "<FromUserName></FromUserName>" +
                "<CreateTime>123456789</CreateTime>" +
                "<EmptyTag/>" +
                "</xml>";
        
        Map<String, String> result = WeworkXmlUtils.parseXmlToMap(xmlString);
        
        assertNotNull(result);
        assertEquals("toUser", result.get("ToUserName"));
        assertEquals("", result.get("FromUserName"));
        assertEquals("123456789", result.get("CreateTime"));
        assertEquals("", result.get("EmptyTag"));
    }
    
    @Test
    public void testMapToXml() {
        // 测试Map转换为XML
        Map<String, String> data = new HashMap<>();
        data.put("ToUserName", "toUser");
        data.put("FromUserName", "fromUser");
        data.put("CreateTime", "123456789");
        data.put("MsgType", "text");
        data.put("Content", "Hello World");
        
        String xmlString = WeworkXmlUtils.mapToXml(data, "xml");
        
        assertNotNull(xmlString);
        assertTrue(xmlString.contains("<ToUserName>"));
        assertTrue(xmlString.contains("<FromUserName>"));
        assertTrue(xmlString.contains("<CreateTime>123456789</CreateTime>"));
        assertTrue(xmlString.contains("<MsgType>"));
        assertTrue(xmlString.contains("Hello World"));
    }
    
    @Test
    public void testMapToXmlWithSpecialCharacters() {
        // 测试包含特殊字符的Map转XML
        Map<String, String> data = new HashMap<>();
        data.put("Content", "Hello <World> & \"Test\"");
        data.put("NormalField", "normal");
        
        String xmlString = WeworkXmlUtils.mapToXml(data, "xml");
        
        assertNotNull(xmlString);
        // 包含特殊字符的字段应该使用CDATA
        assertTrue(xmlString.contains("<![CDATA[Hello <World> & \"Test\"]]>"));
        // 普通字段不使用CDATA
        assertTrue(xmlString.contains("<NormalField>normal</NormalField>"));
    }
    
    @Test
    public void testCreateSimpleResponse() {
        // 测试创建简单响应
        String response = WeworkXmlUtils.createSimpleResponse("text", "Hello World");
        
        assertNotNull(response);
        assertTrue(response.contains("<MsgType>text</MsgType>"));
        assertTrue(response.contains("Hello World"));
        assertTrue(response.contains("<CreateTime>"));
    }
    
    @Test
    public void testCreateEmptyResponse() {
        // 测试创建空响应
        String response = WeworkXmlUtils.createEmptyResponse();
        
        assertNotNull(response);
        assertEquals("", response);
    }
    
    @Test
    public void testExtractEncryptData() {
        // 测试提取加密数据
        String xmlString = "<xml>" +
                "<Encrypt><![CDATA[encrypted_data_here]]></Encrypt>" +
                "<MsgSignature><![CDATA[signature]]></MsgSignature>" +
                "<TimeStamp>123456789</TimeStamp>" +
                "<Nonce><![CDATA[nonce]]></Nonce>" +
                "</xml>";
        
        String encryptData = WeworkXmlUtils.extractEncryptData(xmlString);
        
        assertNotNull(encryptData);
        assertEquals("encrypted_data_here", encryptData);
    }
    
    @Test
    public void testExtractEncryptDataFromInvalidXml() {
        // 测试从无效XML提取加密数据
        String invalidXml = "invalid xml content";
        
        String encryptData = WeworkXmlUtils.extractEncryptData(invalidXml);
        
        assertNull(encryptData);
    }
    
    @Test
    public void testIsValidXml() {
        // 测试有效XML验证
        String validXml = "<xml><test>value</test></xml>";
        String invalidXml = "<xml><test>value</test>";
        String emptyString = "";
        String nullString = null;
        
        assertTrue(WeworkXmlUtils.isValidXml(validXml));
        assertFalse(WeworkXmlUtils.isValidXml(invalidXml));
        assertFalse(WeworkXmlUtils.isValidXml(emptyString));
        assertFalse(WeworkXmlUtils.isValidXml(nullString));
    }
    
    @Test
    public void testParseInvalidXml() {
        // 测试解析无效XML
        String invalidXml = "<xml><test>value</test>";
        
        assertThrows(RuntimeException.class, () -> {
            WeworkXmlUtils.parseXmlToMap(invalidXml);
        });
    }
    
    @Test
    public void testMapToXmlWithNullValues() {
        // 测试包含null值的Map转XML
        Map<String, String> data = new HashMap<>();
        data.put("ValidField", "value");
        data.put("NullField", null);
        
        String xmlString = WeworkXmlUtils.mapToXml(data, "xml");
        
        assertNotNull(xmlString);
        assertTrue(xmlString.contains("<ValidField>value</ValidField>"));
        // null值字段应该被处理
        assertTrue(xmlString.contains("<NullField"));
    }
    
    @Test
    public void testParseXmlWithNamespaces() {
        // 测试解析带命名空间的XML
        String xmlWithNamespace = "<xml xmlns:ns=\"http://example.com\">" +
                "<ns:ToUserName><![CDATA[toUser]]></ns:ToUserName>" +
                "<FromUserName><![CDATA[fromUser]]></FromUserName>" +
                "</xml>";
        
        Map<String, String> result = WeworkXmlUtils.parseXmlToMap(xmlWithNamespace);
        
        assertNotNull(result);
        // 应该能正确解析带命名空间的元素
        assertTrue(result.containsKey("ToUserName") || result.containsKey("ns:ToUserName"));
        assertEquals("fromUser", result.get("FromUserName"));
    }
    
    @Test
    public void testRoundTripConversion() {
        // 测试往返转换（Map -> XML -> Map）
        Map<String, String> originalData = new HashMap<>();
        originalData.put("Field1", "Value1");
        originalData.put("Field2", "Value2");
        originalData.put("Field3", "Special <>&\" Characters");
        
        // Map转XML
        String xmlString = WeworkXmlUtils.mapToXml(originalData, "xml");
        
        // XML转Map
        Map<String, String> parsedData = WeworkXmlUtils.parseXmlToMap(xmlString);
        
        // 验证数据一致性
        assertEquals(originalData.get("Field1"), parsedData.get("Field1"));
        assertEquals(originalData.get("Field2"), parsedData.get("Field2"));
        assertEquals(originalData.get("Field3"), parsedData.get("Field3"));
    }
}
