package com.ems.wework.mapper;

import com.ems.wework.domain.WeworkCallbackLog;
import java.util.List;

/**
 * 企业微信回调日志Mapper接口
 * 
 * <AUTHOR>
 */
public interface WeworkCallbackLogMapper {
    
    /**
     * 查询企业微信回调日志
     * 
     * @param logId 企业微信回调日志主键
     * @return 企业微信回调日志
     */
    public WeworkCallbackLog selectWeworkCallbackLogByLogId(Long logId);

    /**
     * 查询企业微信回调日志列表
     * 
     * @param weworkCallbackLog 企业微信回调日志
     * @return 企业微信回调日志集合
     */
    public List<WeworkCallbackLog> selectWeworkCallbackLogList(WeworkCallbackLog weworkCallbackLog);

    /**
     * 新增企业微信回调日志
     * 
     * @param weworkCallbackLog 企业微信回调日志
     * @return 结果
     */
    public int insertWeworkCallbackLog(WeworkCallbackLog weworkCallbackLog);

    /**
     * 修改企业微信回调日志
     * 
     * @param weworkCallbackLog 企业微信回调日志
     * @return 结果
     */
    public int updateWeworkCallbackLog(WeworkCallbackLog weworkCallbackLog);

    /**
     * 删除企业微信回调日志
     * 
     * @param logId 企业微信回调日志主键
     * @return 结果
     */
    public int deleteWeworkCallbackLogByLogId(Long logId);

    /**
     * 批量删除企业微信回调日志
     * 
     * @param logIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteWeworkCallbackLogByLogIds(Long[] logIds);

    /**
     * 清理指定天数前的日志
     * 
     * @param days 保留天数
     * @return 删除的记录数
     */
    public int cleanOldLogs(int days);
}
