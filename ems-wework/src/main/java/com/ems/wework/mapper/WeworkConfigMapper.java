package com.ems.wework.mapper;

import com.ems.wework.domain.WeworkConfig;
import java.util.List;

/**
 * 企业微信配置Mapper接口
 * 
 * <AUTHOR>
 */
public interface WeworkConfigMapper {
    
    /**
     * 查询企业微信配置
     * 
     * @param configId 企业微信配置主键
     * @return 企业微信配置
     */
    public WeworkConfig selectWeworkConfigByConfigId(Long configId);

    /**
     * 根据企业ID查询企业微信配置
     * 
     * @param corpId 企业ID
     * @return 企业微信配置
     */
    public WeworkConfig selectWeworkConfigByCorpId(String corpId);

    /**
     * 查询企业微信配置列表
     * 
     * @param weworkConfig 企业微信配置
     * @return 企业微信配置集合
     */
    public List<WeworkConfig> selectWeworkConfigList(WeworkConfig weworkConfig);

    /**
     * 新增企业微信配置
     * 
     * @param weworkConfig 企业微信配置
     * @return 结果
     */
    public int insertWeworkConfig(WeworkConfig weworkConfig);

    /**
     * 修改企业微信配置
     * 
     * @param weworkConfig 企业微信配置
     * @return 结果
     */
    public int updateWeworkConfig(WeworkConfig weworkConfig);

    /**
     * 删除企业微信配置
     * 
     * @param configId 企业微信配置主键
     * @return 结果
     */
    public int deleteWeworkConfigByConfigId(Long configId);

    /**
     * 批量删除企业微信配置
     * 
     * @param configIds 需要删除的数据主键集合
     * @return 结果
     */
    public int deleteWeworkConfigByConfigIds(Long[] configIds);
}
