package com.ems.wework.domain.dto;

import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * 企业微信消息DTO
 * 
 * <AUTHOR>
 */
public class WeworkMessage {
    
    /** 接收成员ID列表 */
    @JsonProperty("touser")
    private String toUser;
    
    /** 接收部门ID列表 */
    @JsonProperty("toparty")
    private String toParty;
    
    /** 接收标签ID列表 */
    @JsonProperty("totag")
    private String toTag;
    
    /** 消息类型 */
    @JsonProperty("msgtype")
    private String msgType;
    
    /** 应用ID */
    @JsonProperty("agentid")
    private String agentId;
    
    /** 文本消息内容 */
    @JsonProperty("text")
    private TextContent text;
    
    /** 图片消息内容 */
    @JsonProperty("image")
    private MediaContent image;
    
    /** 语音消息内容 */
    @JsonProperty("voice")
    private MediaContent voice;
    
    /** 视频消息内容 */
    @JsonProperty("video")
    private VideoContent video;
    
    /** 文件消息内容 */
    @JsonProperty("file")
    private MediaContent file;
    
    /** 文本卡片消息内容 */
    @JsonProperty("textcard")
    private TextCardContent textCard;
    
    /** 图文消息内容 */
    @JsonProperty("news")
    private NewsContent news;
    
    /** 是否保密消息 */
    @JsonProperty("safe")
    private Integer safe;
    
    /** 是否开启id转译 */
    @JsonProperty("enable_id_trans")
    private Integer enableIdTrans;
    
    /** 是否开启重复消息检查 */
    @JsonProperty("enable_duplicate_check")
    private Integer enableDuplicateCheck;
    
    /** 重复消息检查的时间间隔 */
    @JsonProperty("duplicate_check_interval")
    private Integer duplicateCheckInterval;
    
    /**
     * 文本消息内容
     */
    public static class TextContent {
        @JsonProperty("content")
        private String content;
        
        public TextContent() {}
        
        public TextContent(String content) {
            this.content = content;
        }
        
        public String getContent() {
            return content;
        }
        
        public void setContent(String content) {
            this.content = content;
        }
    }
    
    /**
     * 媒体消息内容（图片、语音、文件）
     */
    public static class MediaContent {
        @JsonProperty("media_id")
        private String mediaId;
        
        public MediaContent() {}
        
        public MediaContent(String mediaId) {
            this.mediaId = mediaId;
        }
        
        public String getMediaId() {
            return mediaId;
        }
        
        public void setMediaId(String mediaId) {
            this.mediaId = mediaId;
        }
    }
    
    /**
     * 视频消息内容
     */
    public static class VideoContent {
        @JsonProperty("media_id")
        private String mediaId;
        
        @JsonProperty("title")
        private String title;
        
        @JsonProperty("description")
        private String description;
        
        public VideoContent() {}
        
        public VideoContent(String mediaId, String title, String description) {
            this.mediaId = mediaId;
            this.title = title;
            this.description = description;
        }
        
        public String getMediaId() {
            return mediaId;
        }
        
        public void setMediaId(String mediaId) {
            this.mediaId = mediaId;
        }
        
        public String getTitle() {
            return title;
        }
        
        public void setTitle(String title) {
            this.title = title;
        }
        
        public String getDescription() {
            return description;
        }
        
        public void setDescription(String description) {
            this.description = description;
        }
    }
    
    /**
     * 文本卡片消息内容
     */
    public static class TextCardContent {
        @JsonProperty("title")
        private String title;
        
        @JsonProperty("description")
        private String description;
        
        @JsonProperty("url")
        private String url;
        
        @JsonProperty("btntxt")
        private String btnTxt;
        
        public TextCardContent() {}
        
        public TextCardContent(String title, String description, String url, String btnTxt) {
            this.title = title;
            this.description = description;
            this.url = url;
            this.btnTxt = btnTxt;
        }
        
        public String getTitle() {
            return title;
        }
        
        public void setTitle(String title) {
            this.title = title;
        }
        
        public String getDescription() {
            return description;
        }
        
        public void setDescription(String description) {
            this.description = description;
        }
        
        public String getUrl() {
            return url;
        }
        
        public void setUrl(String url) {
            this.url = url;
        }
        
        public String getBtnTxt() {
            return btnTxt;
        }
        
        public void setBtnTxt(String btnTxt) {
            this.btnTxt = btnTxt;
        }
    }
    
    /**
     * 图文消息内容
     */
    public static class NewsContent {
        @JsonProperty("articles")
        private Article[] articles;
        
        public NewsContent() {}
        
        public NewsContent(Article[] articles) {
            this.articles = articles;
        }
        
        public Article[] getArticles() {
            return articles;
        }
        
        public void setArticles(Article[] articles) {
            this.articles = articles;
        }
        
        public static class Article {
            @JsonProperty("title")
            private String title;
            
            @JsonProperty("description")
            private String description;
            
            @JsonProperty("url")
            private String url;
            
            @JsonProperty("picurl")
            private String picUrl;
            
            public Article() {}
            
            public Article(String title, String description, String url, String picUrl) {
                this.title = title;
                this.description = description;
                this.url = url;
                this.picUrl = picUrl;
            }
            
            public String getTitle() {
                return title;
            }
            
            public void setTitle(String title) {
                this.title = title;
            }
            
            public String getDescription() {
                return description;
            }
            
            public void setDescription(String description) {
                this.description = description;
            }
            
            public String getUrl() {
                return url;
            }
            
            public void setUrl(String url) {
                this.url = url;
            }
            
            public String getPicUrl() {
                return picUrl;
            }
            
            public void setPicUrl(String picUrl) {
                this.picUrl = picUrl;
            }
        }
    }
    
    // Getters and Setters
    public String getToUser() {
        return toUser;
    }
    
    public void setToUser(String toUser) {
        this.toUser = toUser;
    }
    
    public String getToParty() {
        return toParty;
    }
    
    public void setToParty(String toParty) {
        this.toParty = toParty;
    }
    
    public String getToTag() {
        return toTag;
    }
    
    public void setToTag(String toTag) {
        this.toTag = toTag;
    }
    
    public String getMsgType() {
        return msgType;
    }
    
    public void setMsgType(String msgType) {
        this.msgType = msgType;
    }
    
    public String getAgentId() {
        return agentId;
    }
    
    public void setAgentId(String agentId) {
        this.agentId = agentId;
    }
    
    public TextContent getText() {
        return text;
    }
    
    public void setText(TextContent text) {
        this.text = text;
    }
    
    public MediaContent getImage() {
        return image;
    }
    
    public void setImage(MediaContent image) {
        this.image = image;
    }
    
    public MediaContent getVoice() {
        return voice;
    }
    
    public void setVoice(MediaContent voice) {
        this.voice = voice;
    }
    
    public VideoContent getVideo() {
        return video;
    }
    
    public void setVideo(VideoContent video) {
        this.video = video;
    }
    
    public MediaContent getFile() {
        return file;
    }
    
    public void setFile(MediaContent file) {
        this.file = file;
    }
    
    public TextCardContent getTextCard() {
        return textCard;
    }
    
    public void setTextCard(TextCardContent textCard) {
        this.textCard = textCard;
    }
    
    public NewsContent getNews() {
        return news;
    }
    
    public void setNews(NewsContent news) {
        this.news = news;
    }
    
    public Integer getSafe() {
        return safe;
    }
    
    public void setSafe(Integer safe) {
        this.safe = safe;
    }
    
    public Integer getEnableIdTrans() {
        return enableIdTrans;
    }
    
    public void setEnableIdTrans(Integer enableIdTrans) {
        this.enableIdTrans = enableIdTrans;
    }
    
    public Integer getEnableDuplicateCheck() {
        return enableDuplicateCheck;
    }
    
    public void setEnableDuplicateCheck(Integer enableDuplicateCheck) {
        this.enableDuplicateCheck = enableDuplicateCheck;
    }
    
    public Integer getDuplicateCheckInterval() {
        return duplicateCheckInterval;
    }
    
    public void setDuplicateCheckInterval(Integer duplicateCheckInterval) {
        this.duplicateCheckInterval = duplicateCheckInterval;
    }
}
