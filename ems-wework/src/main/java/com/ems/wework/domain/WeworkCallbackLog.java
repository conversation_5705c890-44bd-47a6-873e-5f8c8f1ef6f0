package com.ems.wework.domain;

import com.ems.common.annotation.Excel;
import com.ems.common.core.domain.BaseEntity;
import com.fasterxml.jackson.annotation.JsonFormat;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;

import java.util.Date;

/**
 * 企业微信回调日志对象 wework_callback_log
 * 
 * <AUTHOR>
 */
public class WeworkCallbackLog extends BaseEntity {
    
    private static final long serialVersionUID = 1L;

    /** 日志ID */
    private Long logId;

    /** 消息类型 */
    @Excel(name = "消息类型")
    private String msgType;

    /** 事件类型 */
    @Excel(name = "事件类型")
    private String eventType;

    /** 数据回调InfoType */
    @Excel(name = "InfoType")
    private String infoType;

    /** 发送方用户ID */
    @Excel(name = "发送方用户ID")
    private String fromUser;

    /** 接收方用户ID */
    @Excel(name = "接收方用户ID")
    private String toUser;

    /** 应用ID */
    @Excel(name = "应用ID")
    private String agentId;

    /** 消息内容 */
    @Excel(name = "消息内容")
    private String msgContent;

    /** 响应内容 */
    @Excel(name = "响应内容")
    private String responseContent;

    /** 处理状态 */
    @Excel(name = "处理状态", readConverterExp = "0=失败,1=成功")
    private String processStatus;

    /** 错误信息 */
    @Excel(name = "错误信息")
    private String errorMsg;

    /** 处理耗时 */
    @Excel(name = "处理耗时(毫秒)")
    private Long processTime;

    /** 请求IP */
    @Excel(name = "请求IP")
    private String requestIp;

    /** 用户代理 */
    @Excel(name = "用户代理")
    private String userAgent;

    public void setLogId(Long logId) {
        this.logId = logId;
    }

    public Long getLogId() {
        return logId;
    }

    public void setMsgType(String msgType) {
        this.msgType = msgType;
    }

    public String getMsgType() {
        return msgType;
    }

    public void setEventType(String eventType) {
        this.eventType = eventType;
    }

    public String getEventType() {
        return eventType;
    }

    public void setInfoType(String infoType) {
        this.infoType = infoType;
    }

    public String getInfoType() {
        return infoType;
    }

    public void setFromUser(String fromUser) {
        this.fromUser = fromUser;
    }

    public String getFromUser() {
        return fromUser;
    }

    public void setToUser(String toUser) {
        this.toUser = toUser;
    }

    public String getToUser() {
        return toUser;
    }

    public void setAgentId(String agentId) {
        this.agentId = agentId;
    }

    public String getAgentId() {
        return agentId;
    }

    public void setMsgContent(String msgContent) {
        this.msgContent = msgContent;
    }

    public String getMsgContent() {
        return msgContent;
    }

    public void setResponseContent(String responseContent) {
        this.responseContent = responseContent;
    }

    public String getResponseContent() {
        return responseContent;
    }

    public void setProcessStatus(String processStatus) {
        this.processStatus = processStatus;
    }

    public String getProcessStatus() {
        return processStatus;
    }

    public void setErrorMsg(String errorMsg) {
        this.errorMsg = errorMsg;
    }

    public String getErrorMsg() {
        return errorMsg;
    }

    public void setProcessTime(Long processTime) {
        this.processTime = processTime;
    }

    public Long getProcessTime() {
        return processTime;
    }

    public void setRequestIp(String requestIp) {
        this.requestIp = requestIp;
    }

    public String getRequestIp() {
        return requestIp;
    }

    public void setUserAgent(String userAgent) {
        this.userAgent = userAgent;
    }

    public String getUserAgent() {
        return userAgent;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
            .append("logId", getLogId())
            .append("msgType", getMsgType())
            .append("eventType", getEventType())
            .append("infoType", getInfoType())
            .append("fromUser", getFromUser())
            .append("toUser", getToUser())
            .append("agentId", getAgentId())
            .append("msgContent", getMsgContent())
            .append("responseContent", getResponseContent())
            .append("processStatus", getProcessStatus())
            .append("errorMsg", getErrorMsg())
            .append("processTime", getProcessTime())
            .append("requestIp", getRequestIp())
            .append("userAgent", getUserAgent())
            .append("createTime", getCreateTime())
            .toString();
    }
}
