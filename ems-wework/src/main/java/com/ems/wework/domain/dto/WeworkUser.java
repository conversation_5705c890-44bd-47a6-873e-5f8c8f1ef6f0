package com.ems.wework.domain.dto;

import com.fasterxml.jackson.annotation.JsonProperty;

import java.util.List;

/**
 * 企业微信用户信息DTO
 * 
 * <AUTHOR>
 */
public class WeworkUser {
    
    /** 用户ID */
    @JsonProperty("userid")
    private String userId;
    
    /** 用户姓名 */
    @JsonProperty("name")
    private String name;
    
    /** 手机号 */
    @JsonProperty("mobile")
    private String mobile;
    
    /** 部门ID列表 */
    @JsonProperty("department")
    private List<Integer> department;
    
    /** 职务信息 */
    @JsonProperty("position")
    private String position;
    
    /** 性别 */
    @JsonProperty("gender")
    private String gender;
    
    /** 邮箱 */
    @JsonProperty("email")
    private String email;
    
    /** 头像URL */
    @JsonProperty("avatar")
    private String avatar;
    
    /** 座机 */
    @JsonProperty("telephone")
    private String telephone;
    
    /** 别名 */
    @JsonProperty("alias")
    private String alias;
    
    /** 地址 */
    @JsonProperty("address")
    private String address;
    
    /** 扩展属性 */
    @JsonProperty("extattr")
    private Object extattr;
    
    /** 激活状态：1=已激活，2=已禁用，4=未激活，5=退出企业 */
    @JsonProperty("status")
    private Integer status;
    
    /** 是否启用：1启用，0禁用 */
    @JsonProperty("enable")
    private Integer enable;
    
    /** 是否隐藏手机号 */
    @JsonProperty("hide_mobile")
    private Integer hideMobile;
    
    /** 英文名 */
    @JsonProperty("english_name")
    private String englishName;
    
    /** 头像缩略图 */
    @JsonProperty("thumb_avatar")
    private String thumbAvatar;
    
    /** 二维码 */
    @JsonProperty("qr_code")
    private String qrCode;
    
    /** 对外职务 */
    @JsonProperty("external_position")
    private String externalPosition;
    
    /** 外部联系人权限 */
    @JsonProperty("external_profile")
    private Object externalProfile;
    
    // Getters and Setters
    public String getUserId() {
        return userId;
    }
    
    public void setUserId(String userId) {
        this.userId = userId;
    }
    
    public String getName() {
        return name;
    }
    
    public void setName(String name) {
        this.name = name;
    }
    
    public String getMobile() {
        return mobile;
    }
    
    public void setMobile(String mobile) {
        this.mobile = mobile;
    }
    
    public List<Integer> getDepartment() {
        return department;
    }
    
    public void setDepartment(List<Integer> department) {
        this.department = department;
    }
    
    public String getPosition() {
        return position;
    }
    
    public void setPosition(String position) {
        this.position = position;
    }
    
    public String getGender() {
        return gender;
    }
    
    public void setGender(String gender) {
        this.gender = gender;
    }
    
    public String getEmail() {
        return email;
    }
    
    public void setEmail(String email) {
        this.email = email;
    }
    
    public String getAvatar() {
        return avatar;
    }
    
    public void setAvatar(String avatar) {
        this.avatar = avatar;
    }
    
    public String getTelephone() {
        return telephone;
    }
    
    public void setTelephone(String telephone) {
        this.telephone = telephone;
    }
    
    public String getAlias() {
        return alias;
    }
    
    public void setAlias(String alias) {
        this.alias = alias;
    }
    
    public String getAddress() {
        return address;
    }
    
    public void setAddress(String address) {
        this.address = address;
    }
    
    public Object getExtattr() {
        return extattr;
    }
    
    public void setExtattr(Object extattr) {
        this.extattr = extattr;
    }
    
    public Integer getStatus() {
        return status;
    }
    
    public void setStatus(Integer status) {
        this.status = status;
    }
    
    public Integer getEnable() {
        return enable;
    }
    
    public void setEnable(Integer enable) {
        this.enable = enable;
    }
    
    public Integer getHideMobile() {
        return hideMobile;
    }
    
    public void setHideMobile(Integer hideMobile) {
        this.hideMobile = hideMobile;
    }
    
    public String getEnglishName() {
        return englishName;
    }
    
    public void setEnglishName(String englishName) {
        this.englishName = englishName;
    }
    
    public String getThumbAvatar() {
        return thumbAvatar;
    }
    
    public void setThumbAvatar(String thumbAvatar) {
        this.thumbAvatar = thumbAvatar;
    }
    
    public String getQrCode() {
        return qrCode;
    }
    
    public void setQrCode(String qrCode) {
        this.qrCode = qrCode;
    }
    
    public String getExternalPosition() {
        return externalPosition;
    }
    
    public void setExternalPosition(String externalPosition) {
        this.externalPosition = externalPosition;
    }
    
    public Object getExternalProfile() {
        return externalProfile;
    }
    
    public void setExternalProfile(Object externalProfile) {
        this.externalProfile = externalProfile;
    }
}
