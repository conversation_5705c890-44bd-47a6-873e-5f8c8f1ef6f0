package com.ems.wework.domain;

import com.ems.common.annotation.Excel;
import com.ems.common.core.domain.BaseEntity;
import org.apache.commons.lang3.builder.ToStringBuilder;
import org.apache.commons.lang3.builder.ToStringStyle;
import org.apache.ibatis.type.Alias;

/**
 * 企业微信配置对象 wework_config
 * 
 * <AUTHOR>
 */
@Alias("WeworkDomainConfig")
public class WeworkConfig extends BaseEntity {
    
    private static final long serialVersionUID = 1L;

    /** 配置ID */
    private Long configId;

    /** 企业ID */
    @Excel(name = "企业ID")
    private String corpId;

    /** 应用密钥 */
    @Excel(name = "应用密钥")
    private String corpSecret;

    /** 应用ID */
    @Excel(name = "应用ID")
    private String agentId;

    /** 回调Token */
    @Excel(name = "回调Token")
    private String callbackToken;

    /** 回调加密密钥 */
    @Excel(name = "回调加密密钥")
    private String callbackAesKey;

    /** OAuth2回调地址 */
    @Excel(name = "OAuth2回调地址")
    private String oauth2RedirectUri;

    /** 登录开关 */
    @Excel(name = "登录开关", readConverterExp = "0=关闭,1=开启")
    private String loginEnabled;

    /** 自动创建用户 */
    @Excel(name = "自动创建用户", readConverterExp = "0=否,1=是")
    private String autoCreateUser;

    /** 默认角色ID */
    @Excel(name = "默认角色ID")
    private Long defaultRoleId;

    /** 默认部门ID */
    @Excel(name = "默认部门ID")
    private Long defaultDeptId;

    /** 配置状态 */
    @Excel(name = "配置状态", readConverterExp = "0=停用,1=正常")
    private String status;

    public void setConfigId(Long configId) {
        this.configId = configId;
    }

    public Long getConfigId() {
        return configId;
    }

    public void setCorpId(String corpId) {
        this.corpId = corpId;
    }

    public String getCorpId() {
        return corpId;
    }

    public void setCorpSecret(String corpSecret) {
        this.corpSecret = corpSecret;
    }

    public String getCorpSecret() {
        return corpSecret;
    }

    public void setAgentId(String agentId) {
        this.agentId = agentId;
    }

    public String getAgentId() {
        return agentId;
    }

    public void setCallbackToken(String callbackToken) {
        this.callbackToken = callbackToken;
    }

    public String getCallbackToken() {
        return callbackToken;
    }

    public void setCallbackAesKey(String callbackAesKey) {
        this.callbackAesKey = callbackAesKey;
    }

    public String getCallbackAesKey() {
        return callbackAesKey;
    }

    public void setOauth2RedirectUri(String oauth2RedirectUri) {
        this.oauth2RedirectUri = oauth2RedirectUri;
    }

    public String getOauth2RedirectUri() {
        return oauth2RedirectUri;
    }

    public void setLoginEnabled(String loginEnabled) {
        this.loginEnabled = loginEnabled;
    }

    public String getLoginEnabled() {
        return loginEnabled;
    }

    public void setAutoCreateUser(String autoCreateUser) {
        this.autoCreateUser = autoCreateUser;
    }

    public String getAutoCreateUser() {
        return autoCreateUser;
    }

    public void setDefaultRoleId(Long defaultRoleId) {
        this.defaultRoleId = defaultRoleId;
    }

    public Long getDefaultRoleId() {
        return defaultRoleId;
    }

    public void setDefaultDeptId(Long defaultDeptId) {
        this.defaultDeptId = defaultDeptId;
    }

    public Long getDefaultDeptId() {
        return defaultDeptId;
    }

    public void setStatus(String status) {
        this.status = status;
    }

    public String getStatus() {
        return status;
    }

    @Override
    public String toString() {
        return new ToStringBuilder(this, ToStringStyle.MULTI_LINE_STYLE)
            .append("configId", getConfigId())
            .append("corpId", getCorpId())
            .append("corpSecret", getCorpSecret())
            .append("agentId", getAgentId())
            .append("callbackToken", getCallbackToken())
            .append("callbackAesKey", getCallbackAesKey())
            .append("oauth2RedirectUri", getOauth2RedirectUri())
            .append("loginEnabled", getLoginEnabled())
            .append("autoCreateUser", getAutoCreateUser())
            .append("defaultRoleId", getDefaultRoleId())
            .append("defaultDeptId", getDefaultDeptId())
            .append("status", getStatus())
            .append("createBy", getCreateBy())
            .append("createTime", getCreateTime())
            .append("updateBy", getUpdateBy())
            .append("updateTime", getUpdateTime())
            .append("remark", getRemark())
            .toString();
    }
}
