package com.ems.wework.domain.dto;

/**
 * 企业微信登录请求体
 * 
 * <AUTHOR>
 */
public class WeworkLoginBody {
    
    /** 授权码 */
    private String code;
    
    /** 状态参数 */
    private String state;
    
    /** 企业ID */
    private String corpId;
    
    /** 应用ID */
    private String agentId;
    
    /** 重定向URI */
    private String redirectUri;
    
    public WeworkLoginBody() {}
    
    public WeworkLoginBody(String code, String state) {
        this.code = code;
        this.state = state;
    }
    
    public String getCode() {
        return code;
    }
    
    public void setCode(String code) {
        this.code = code;
    }
    
    public String getState() {
        return state;
    }
    
    public void setState(String state) {
        this.state = state;
    }
    
    public String getCorpId() {
        return corpId;
    }
    
    public void setCorpId(String corpId) {
        this.corpId = corpId;
    }
    
    public String getAgentId() {
        return agentId;
    }
    
    public void setAgentId(String agentId) {
        this.agentId = agentId;
    }
    
    public String getRedirectUri() {
        return redirectUri;
    }
    
    public void setRedirectUri(String redirectUri) {
        this.redirectUri = redirectUri;
    }
}
