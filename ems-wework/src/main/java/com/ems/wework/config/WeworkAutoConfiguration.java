package com.ems.wework.config;

import org.apache.http.client.HttpClient;
import org.apache.http.impl.client.HttpClientBuilder;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.context.properties.EnableConfigurationProperties;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.ComponentScan;
import org.springframework.context.annotation.Configuration;
import org.springframework.http.client.HttpComponentsClientHttpRequestFactory;
import org.springframework.web.client.RestTemplate;

/**
 * 企业微信自动配置类
 * 
 * <AUTHOR>
 */
@Configuration
@EnableConfigurationProperties(WeworkConfig.class)
@ComponentScan(basePackages = "com.ems.wework")
public class WeworkAutoConfiguration {
    
    /**
     * 配置HTTP客户端
     */
    @Bean("weworkHttpClient")
    @ConditionalOnMissingBean(name = "weworkHttpClient")
    public HttpClient weworkHttpClient(WeworkConfig weworkConfig) {
        return HttpClientBuilder.create()
                .setMaxConnTotal(200)
                .setMaxConnPerRoute(50)
                .build();
    }
    
    /**
     * 配置RestTemplate
     */
    @Bean("weworkRestTemplate")
    @ConditionalOnMissingBean(name = "weworkRestTemplate")
    public RestTemplate weworkRestTemplate(WeworkConfig weworkConfig) {
        HttpComponentsClientHttpRequestFactory factory = new HttpComponentsClientHttpRequestFactory();
        factory.setConnectTimeout(weworkConfig.getApi().getConnectTimeout());
        factory.setReadTimeout(weworkConfig.getApi().getReadTimeout());
        
        return new RestTemplate(factory);
    }
}
