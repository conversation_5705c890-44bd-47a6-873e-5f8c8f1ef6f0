package com.ems.wework.config;

import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.stereotype.Component;

/**
 * 企业微信配置类
 * 
 * <AUTHOR>
 */
@Component
@ConfigurationProperties(prefix = "wework")
public class WeworkConfig {
    
    /** 应用类型：self_built(自建应用) 或 third_party(第三方应用) */
    private String appType = "self_built";

    /** 企业ID */
    private String corpId;

    /** 应用密钥 */
    private String corpSecret;

    /** 应用ID */
    private String agentId;

    /** 第三方应用ID（仅第三方应用使用） */
    private String suiteId;

    /** 第三方应用密钥（仅第三方应用使用） */
    private String suiteSecret;
    
    /** 回调配置 */
    private Callback callback = new Callback();
    
    /** OAuth2配置 */
    private OAuth2 oauth2 = new OAuth2();
    
    /** API配置 */
    private Api api = new Api();
    
    /**
     * 回调配置
     */
    public static class Callback {
        /** 回调Token */
        private String token;
        
        /** 回调加密密钥 */
        private String encodingAesKey;
        
        /** 数据回调URL */
        private String dataUrl = "/wework/callback/data";
        
        /** 指令回调URL */
        private String commandUrl = "/wework/callback/command";
        
        public String getToken() {
            return token;
        }
        
        public void setToken(String token) {
            this.token = token;
        }
        
        public String getEncodingAesKey() {
            return encodingAesKey;
        }
        
        public void setEncodingAesKey(String encodingAesKey) {
            this.encodingAesKey = encodingAesKey;
        }
        
        public String getDataUrl() {
            return dataUrl;
        }
        
        public void setDataUrl(String dataUrl) {
            this.dataUrl = dataUrl;
        }
        
        public String getCommandUrl() {
            return commandUrl;
        }
        
        public void setCommandUrl(String commandUrl) {
            this.commandUrl = commandUrl;
        }
    }
    
    /**
     * OAuth2配置
     */
    public static class OAuth2 {
        /** 授权回调地址 */
        private String redirectUri;
        
        /** 授权scope */
        private String scope = "snsapi_base";
        
        /** 登录开关 */
        private boolean enabled = true;
        
        public String getRedirectUri() {
            return redirectUri;
        }
        
        public void setRedirectUri(String redirectUri) {
            this.redirectUri = redirectUri;
        }
        
        public String getScope() {
            return scope;
        }
        
        public void setScope(String scope) {
            this.scope = scope;
        }
        
        public boolean isEnabled() {
            return enabled;
        }
        
        public void setEnabled(boolean enabled) {
            this.enabled = enabled;
        }
    }
    
    /**
     * API配置
     */
    public static class Api {
        /** API基础URL */
        private String baseUrl = "https://qyapi.weixin.qq.com";
        
        /** 连接超时时间（毫秒） */
        private int connectTimeout = 5000;
        
        /** 读取超时时间（毫秒） */
        private int readTimeout = 10000;
        
        /** 重试次数 */
        private int retryCount = 3;
        
        public String getBaseUrl() {
            return baseUrl;
        }
        
        public void setBaseUrl(String baseUrl) {
            this.baseUrl = baseUrl;
        }
        
        public int getConnectTimeout() {
            return connectTimeout;
        }
        
        public void setConnectTimeout(int connectTimeout) {
            this.connectTimeout = connectTimeout;
        }
        
        public int getReadTimeout() {
            return readTimeout;
        }
        
        public void setReadTimeout(int readTimeout) {
            this.readTimeout = readTimeout;
        }
        
        public int getRetryCount() {
            return retryCount;
        }
        
        public void setRetryCount(int retryCount) {
            this.retryCount = retryCount;
        }
    }
    
    // Getters and Setters
    public String getCorpId() {
        return corpId;
    }
    
    public void setCorpId(String corpId) {
        this.corpId = corpId;
    }
    
    public String getCorpSecret() {
        return corpSecret;
    }
    
    public void setCorpSecret(String corpSecret) {
        this.corpSecret = corpSecret;
    }
    
    public String getAgentId() {
        return agentId;
    }
    
    public void setAgentId(String agentId) {
        this.agentId = agentId;
    }

    public String getAppType() {
        return appType;
    }

    public void setAppType(String appType) {
        this.appType = appType;
    }

    public String getSuiteId() {
        return suiteId;
    }

    public void setSuiteId(String suiteId) {
        this.suiteId = suiteId;
    }

    public String getSuiteSecret() {
        return suiteSecret;
    }

    public void setSuiteSecret(String suiteSecret) {
        this.suiteSecret = suiteSecret;
    }

    /**
     * 获取接收者ID（用于加解密）
     * 自建应用返回corpId，第三方应用返回suiteId
     */
    public String getReceiverId() {
        if ("third_party".equals(appType)) {
            return suiteId;
        } else {
            return corpId;
        }
    }

    /**
     * 是否为第三方应用
     */
    public boolean isThirdPartyApp() {
        return "third_party".equals(appType);
    }
    
    public Callback getCallback() {
        return callback;
    }
    
    public void setCallback(Callback callback) {
        this.callback = callback;
    }
    
    public OAuth2 getOauth2() {
        return oauth2;
    }
    
    public void setOauth2(OAuth2 oauth2) {
        this.oauth2 = oauth2;
    }
    
    public Api getApi() {
        return api;
    }
    
    public void setApi(Api api) {
        this.api = api;
    }
}
