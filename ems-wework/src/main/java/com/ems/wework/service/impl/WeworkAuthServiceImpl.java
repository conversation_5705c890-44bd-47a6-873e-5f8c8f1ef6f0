package com.ems.wework.service.impl;

import com.ems.common.core.domain.entity.SysUser;
import com.ems.common.core.domain.model.LoginUser;
import com.ems.common.utils.DateUtils;
import com.ems.common.utils.SecurityUtils;
import com.ems.common.utils.StringUtils;
import com.ems.framework.web.service.TokenService;
import com.ems.system.service.ISysUserService;
import com.ems.wework.config.WeworkConfig;
import com.ems.wework.constants.WeworkConstants;
import com.ems.wework.domain.dto.WeworkUser;
import com.ems.wework.service.IWeworkApiService;
import com.ems.wework.service.IWeworkAuthService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.io.UnsupportedEncodingException;
import java.net.URLEncoder;
import java.util.Date;

/**
 * 企业微信认证服务实现
 * 
 * <AUTHOR>
 */
@Service
public class WeworkAuthServiceImpl implements IWeworkAuthService {
    
    private static final Logger log = LoggerFactory.getLogger(WeworkAuthServiceImpl.class);
    
    @Autowired
    private WeworkConfig weworkConfig;
    
    @Autowired
    private IWeworkApiService apiService;
    
    @Autowired
    private ISysUserService userService;
    
    @Autowired
    private TokenService tokenService;
    
    @Override
    public String buildAuthUrl(String redirectUri, String state) {
        try {
            if (!isWeworkLoginEnabled()) {
                throw new RuntimeException("企业微信登录未启用");
            }
            
            StringBuilder authUrl = new StringBuilder();
            authUrl.append(WeworkConstants.OAUTH2_AUTHORIZE_URL);
            authUrl.append("?appid=").append(weworkConfig.getCorpId());
            authUrl.append("&redirect_uri=").append(URLEncoder.encode(redirectUri, "UTF-8"));
            authUrl.append("&response_type=code");
            authUrl.append("&scope=").append(weworkConfig.getOauth2().getScope());
            authUrl.append("&agentid=").append(weworkConfig.getAgentId());
            
            if (StringUtils.isNotEmpty(state)) {
                authUrl.append("&state=").append(URLEncoder.encode(state, "UTF-8"));
            }
            
            authUrl.append("#wechat_redirect");
            
            log.debug("构建企业微信授权URL：{}", authUrl.toString());
            return authUrl.toString();
            
        } catch (UnsupportedEncodingException e) {
            log.error("构建授权URL失败", e);
            throw new RuntimeException("构建授权URL失败", e);
        }
    }
    
    @Override
    public String buildAuthUrl(String redirectUri) {
        return buildAuthUrl(redirectUri, null);
    }
    
    @Override
    public String login(String code) {
        return login(code, weworkConfig.getCorpId(), weworkConfig.getAgentId());
    }
    
    @Override
    public String login(String code, String corpId, String agentId) {
        try {
            log.info("开始企业微信登录，code: {}, corpId: {}, agentId: {}", code, corpId, agentId);
            
            if (!isWeworkLoginEnabled()) {
                throw new RuntimeException("企业微信登录未启用");
            }
            
            if (StringUtils.isEmpty(code)) {
                throw new RuntimeException("授权码不能为空");
            }
            
            // 1. 获取access_token
            String accessToken = apiService.getAccessToken(corpId, weworkConfig.getCorpSecret());
            if (StringUtils.isEmpty(accessToken)) {
                throw new RuntimeException("获取access_token失败");
            }
            
            // 2. 通过code获取用户信息
            WeworkUser weworkUser = apiService.getUserInfoByCode(accessToken, code);
            if (weworkUser == null) {
                throw new RuntimeException("获取用户信息失败");
            }
            
            // 3. 验证用户状态
            if (!isValidWeworkUser(weworkUser)) {
                throw new RuntimeException("用户状态无效");
            }
            
            // 4. 获取或创建本地用户
            SysUser sysUser = getOrCreateUser(weworkUser);
            if (sysUser == null) {
                throw new RuntimeException("用户创建失败");
            }
            
            // 5. 创建登录用户对象
            LoginUser loginUser = new LoginUser(sysUser.getUserId(), sysUser.getDeptId(), sysUser, null);
            
            // 6. 生成JWT Token
            String token = tokenService.createToken(loginUser);

            // 7. 更新登录信息（这里需要从请求中获取IP，暂时跳过）
            sysUser.setLoginDate(DateUtils.getNowDate());
            userService.updateUserProfile(sysUser);
            
            log.info("企业微信登录成功，用户: {}, token: {}", sysUser.getUserName(), token);
            return token;
            
        } catch (Exception e) {
            log.error("企业微信登录失败", e);
            throw new RuntimeException("企业微信登录失败：" + e.getMessage(), e);
        }
    }
    
    @Override
    public SysUser getOrCreateUser(WeworkUser weworkUser) {
        try {
            // 1. 根据企业微信用户ID查询本地用户
            SysUser existUser = getUserByWeworkId(weworkUser.getUserId());
            
            if (existUser != null) {
                // 2. 用户已存在，同步最新信息
                syncWeworkUser(weworkUser);
                return existUser;
            }
            
            // 3. 用户不存在，检查是否允许自动创建
            // 这里可以根据配置决定是否自动创建用户
            // 暂时返回null，需要手动绑定
            log.warn("企业微信用户不存在且未配置自动创建：{}", weworkUser.getUserId());
            return null;
            
        } catch (Exception e) {
            log.error("获取或创建用户失败", e);
            throw new RuntimeException("获取或创建用户失败", e);
        }
    }
    
    @Override
    public boolean bindWeworkUser(Long userId, WeworkUser weworkUser) {
        try {
            SysUser sysUser = userService.selectUserById(userId);
            if (sysUser == null) {
                log.error("用户不存在：userId={}", userId);
                return false;
            }
            
            // 检查企业微信用户是否已被其他用户绑定
            SysUser existUser = getUserByWeworkId(weworkUser.getUserId());
            if (existUser != null && !existUser.getUserId().equals(userId)) {
                log.error("企业微信用户已被其他用户绑定：weworkUserId={}, existUserId={}", 
                         weworkUser.getUserId(), existUser.getUserId());
                return false;
            }
            
            // 更新用户的企业微信信息
            sysUser.setWeworkUserid(weworkUser.getUserId());
            sysUser.setWeworkMobile(weworkUser.getMobile());
            sysUser.setWeworkAvatar(weworkUser.getAvatar());
            sysUser.setWeworkName(weworkUser.getName());
            sysUser.setWeworkDepartment(weworkUser.getDepartment() != null ? 
                                       weworkUser.getDepartment().toString() : null);
            sysUser.setWeworkBindTime(new Date());
            
            int result = userService.updateUser(sysUser);
            
            log.info("绑定企业微信用户成功：userId={}, weworkUserId={}", userId, weworkUser.getUserId());
            return result > 0;
            
        } catch (Exception e) {
            log.error("绑定企业微信用户失败", e);
            return false;
        }
    }
    
    @Override
    public boolean unbindWeworkUser(Long userId) {
        try {
            SysUser sysUser = userService.selectUserById(userId);
            if (sysUser == null) {
                log.error("用户不存在：userId={}", userId);
                return false;
            }
            
            // 清空企业微信相关信息
            sysUser.setWeworkUserid(null);
            sysUser.setWeworkMobile(null);
            sysUser.setWeworkAvatar(null);
            sysUser.setWeworkName(null);
            sysUser.setWeworkDepartment(null);
            sysUser.setWeworkBindTime(null);
            
            int result = userService.updateUser(sysUser);
            
            log.info("解绑企业微信用户成功：userId={}", userId);
            return result > 0;
            
        } catch (Exception e) {
            log.error("解绑企业微信用户失败", e);
            return false;
        }
    }
    
    @Override
    public SysUser getUserByWeworkId(String weworkUserId) {
        if (StringUtils.isEmpty(weworkUserId)) {
            return null;
        }
        
        try {
            return userService.selectUserByWeworkId(weworkUserId);
        } catch (Exception e) {
            log.error("根据企业微信用户ID查询用户失败：weworkUserId={}", weworkUserId, e);
            return null;
        }
    }
    
    @Override
    public boolean syncWeworkUser(WeworkUser weworkUser) {
        try {
            SysUser sysUser = getUserByWeworkId(weworkUser.getUserId());
            if (sysUser == null) {
                log.warn("用户不存在，无法同步：weworkUserId={}", weworkUser.getUserId());
                return false;
            }
            
            // 同步企业微信信息
            boolean updated = false;
            
            if (!StringUtils.equals(sysUser.getWeworkMobile(), weworkUser.getMobile())) {
                sysUser.setWeworkMobile(weworkUser.getMobile());
                updated = true;
            }
            
            if (!StringUtils.equals(sysUser.getWeworkAvatar(), weworkUser.getAvatar())) {
                sysUser.setWeworkAvatar(weworkUser.getAvatar());
                updated = true;
            }
            
            if (!StringUtils.equals(sysUser.getWeworkName(), weworkUser.getName())) {
                sysUser.setWeworkName(weworkUser.getName());
                updated = true;
            }
            
            String deptStr = weworkUser.getDepartment() != null ? weworkUser.getDepartment().toString() : null;
            if (!StringUtils.equals(sysUser.getWeworkDepartment(), deptStr)) {
                sysUser.setWeworkDepartment(deptStr);
                updated = true;
            }
            
            if (updated) {
                int result = userService.updateUser(sysUser);
                log.info("同步企业微信用户信息成功：weworkUserId={}", weworkUser.getUserId());
                return result > 0;
            }
            
            return true;
            
        } catch (Exception e) {
            log.error("同步企业微信用户信息失败", e);
            return false;
        }
    }
    
    @Override
    public boolean isWeworkLoginEnabled() {
        return weworkConfig.getOauth2().isEnabled();
    }
    
    @Override
    public boolean isValidWeworkUser(WeworkUser weworkUser) {
        if (weworkUser == null || StringUtils.isEmpty(weworkUser.getUserId())) {
            return false;
        }
        
        // 检查用户状态：1=已激活，2=已禁用，4=未激活，5=退出企业
        Integer status = weworkUser.getStatus();
        if (status == null || status != 1) {
            log.warn("企业微信用户状态无效：userId={}, status={}", weworkUser.getUserId(), status);
            return false;
        }
        
        // 检查是否启用：1启用，0禁用
        Integer enable = weworkUser.getEnable();
        if (enable == null || enable != 1) {
            log.warn("企业微信用户已禁用：userId={}, enable={}", weworkUser.getUserId(), enable);
            return false;
        }
        
        return true;
    }
}
