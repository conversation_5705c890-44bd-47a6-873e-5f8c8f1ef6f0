package com.ems.wework.service;

import com.ems.wework.domain.dto.WeworkUser;
import com.ems.wework.domain.dto.WeworkMessage;

/**
 * 企业微信API调用服务接口
 * 
 * <AUTHOR>
 */
public interface IWeworkApiService {
    
    /**
     * 获取第三方应用凭证
     * 
     * @param suiteId 第三方应用ID
     * @param suiteSecret 第三方应用密钥
     * @param suiteTicket 第三方应用票据
     * @return suite_access_token
     */
    String getSuiteAccessToken(String suiteId, String suiteSecret, String suiteTicket);
    
    /**
     * 获取企业永久授权码
     * 
     * @param suiteAccessToken 第三方应用凭证
     * @param authCode 临时授权码
     * @return 永久授权码信息
     */
    String getPermanentCode(String suiteAccessToken, String authCode);
    
    /**
     * 获取企业授权信息
     * 
     * @param suiteAccessToken 第三方应用凭证
     * @param authCorpId 授权企业ID
     * @param permanentCode 永久授权码
     * @return 企业授权信息
     */
    String getAuthInfo(String suiteAccessToken, String authCorpId, String permanentCode);
    
    /**
     * 获取企业access_token
     * 
     * @param corpId 企业ID
     * @param corpSecret 应用密钥
     * @return access_token
     */
    String getAccessToken(String corpId, String corpSecret);
    
    /**
     * 获取企业access_token（第三方应用）
     * 
     * @param suiteAccessToken 第三方应用凭证
     * @param authCorpId 授权企业ID
     * @param permanentCode 永久授权码
     * @return access_token
     */
    String getCorpAccessToken(String suiteAccessToken, String authCorpId, String permanentCode);
    
    /**
     * 通过code获取用户信息
     * 
     * @param accessToken 访问令牌
     * @param code 授权码
     * @return 用户信息
     */
    WeworkUser getUserInfoByCode(String accessToken, String code);
    
    /**
     * 获取用户详细信息
     * 
     * @param accessToken 访问令牌
     * @param userId 用户ID
     * @return 用户详细信息
     */
    WeworkUser getUserDetail(String accessToken, String userId);
    
    /**
     * 发送应用消息
     * 
     * @param accessToken 访问令牌
     * @param message 消息对象
     * @return 发送结果
     */
    String sendMessage(String accessToken, WeworkMessage message);
    
    /**
     * 获取部门列表
     * 
     * @param accessToken 访问令牌
     * @param deptId 部门ID（可选）
     * @return 部门列表
     */
    String getDepartmentList(String accessToken, String deptId);
    
    /**
     * 获取部门成员
     * 
     * @param accessToken 访问令牌
     * @param deptId 部门ID
     * @param fetchChild 是否递归获取子部门成员
     * @return 部门成员列表
     */
    String getDepartmentUsers(String accessToken, String deptId, boolean fetchChild);
    
    /**
     * 创建成员
     * 
     * @param accessToken 访问令牌
     * @param userInfo 用户信息
     * @return 创建结果
     */
    String createUser(String accessToken, String userInfo);
    
    /**
     * 更新成员
     * 
     * @param accessToken 访问令牌
     * @param userInfo 用户信息
     * @return 更新结果
     */
    String updateUser(String accessToken, String userInfo);
    
    /**
     * 删除成员
     * 
     * @param accessToken 访问令牌
     * @param userId 用户ID
     * @return 删除结果
     */
    String deleteUser(String accessToken, String userId);
    
    /**
     * 批量删除成员
     * 
     * @param accessToken 访问令牌
     * @param userIds 用户ID列表
     * @return 删除结果
     */
    String batchDeleteUsers(String accessToken, String[] userIds);
}
