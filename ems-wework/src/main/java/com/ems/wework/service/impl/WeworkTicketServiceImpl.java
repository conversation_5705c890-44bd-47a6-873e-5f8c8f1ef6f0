package com.ems.wework.service.impl;

import com.ems.common.core.redis.RedisCache;
import com.ems.common.utils.StringUtils;
import com.ems.wework.constants.WeworkConstants;
import com.ems.wework.service.IWeworkTicketService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.concurrent.TimeUnit;

/**
 * 企业微信第三方应用票据服务实现
 * 
 * <AUTHOR>
 */
@Service
public class WeworkTicketServiceImpl implements IWeworkTicketService {
    
    private static final Logger log = LoggerFactory.getLogger(WeworkTicketServiceImpl.class);
    
    @Autowired
    private RedisCache redisCache;
    
    @Override
    public void saveSuiteTicket(String suiteId, String suiteTicket) {
        if (StringUtils.isEmpty(suiteId) || StringUtils.isEmpty(suiteTicket)) {
            log.warn("保存suite_ticket失败：参数不能为空");
            return;
        }
        
        String cacheKey = WeworkConstants.CacheKey.SUITE_TICKET_PREFIX + suiteId;
        // suite_ticket有效期为30分钟，这里设置35分钟确保不会过期
        redisCache.setCacheObject(cacheKey, suiteTicket, 35, TimeUnit.MINUTES);
        
        log.debug("保存suite_ticket成功，suiteId: {}", suiteId);
    }
    
    @Override
    public String getSuiteTicket(String suiteId) {
        if (StringUtils.isEmpty(suiteId)) {
            log.warn("获取suite_ticket失败：suiteId不能为空");
            return null;
        }
        
        String cacheKey = WeworkConstants.CacheKey.SUITE_TICKET_PREFIX + suiteId;
        String suiteTicket = redisCache.getCacheObject(cacheKey);
        
        if (StringUtils.isEmpty(suiteTicket)) {
            log.warn("获取suite_ticket失败：缓存中不存在，suiteId: {}", suiteId);
        } else {
            log.debug("获取suite_ticket成功，suiteId: {}", suiteId);
        }
        
        return suiteTicket;
    }
    
    @Override
    public void saveSuiteAccessToken(String suiteId, String suiteAccessToken, int expiresIn) {
        if (StringUtils.isEmpty(suiteId) || StringUtils.isEmpty(suiteAccessToken)) {
            log.warn("保存suite_access_token失败：参数不能为空");
            return;
        }
        
        String cacheKey = WeworkConstants.CacheKey.SUITE_ACCESS_TOKEN_PREFIX + suiteId;
        // 提前5分钟过期，避免使用过期token
        int cacheTime = Math.max(expiresIn - 300, 60);
        redisCache.setCacheObject(cacheKey, suiteAccessToken, cacheTime, TimeUnit.SECONDS);
        
        log.debug("保存suite_access_token成功，suiteId: {}, expiresIn: {}秒", suiteId, expiresIn);
    }
    
    @Override
    public String getSuiteAccessToken(String suiteId) {
        if (StringUtils.isEmpty(suiteId)) {
            log.warn("获取suite_access_token失败：suiteId不能为空");
            return null;
        }
        
        String cacheKey = WeworkConstants.CacheKey.SUITE_ACCESS_TOKEN_PREFIX + suiteId;
        String suiteAccessToken = redisCache.getCacheObject(cacheKey);
        
        if (StringUtils.isEmpty(suiteAccessToken)) {
            log.warn("获取suite_access_token失败：缓存中不存在或已过期，suiteId: {}", suiteId);
        } else {
            log.debug("获取suite_access_token成功，suiteId: {}", suiteId);
        }
        
        return suiteAccessToken;
    }
    
    @Override
    public void saveAccessToken(String corpId, String agentId, String accessToken, int expiresIn) {
        if (StringUtils.isEmpty(corpId) || StringUtils.isEmpty(agentId) || StringUtils.isEmpty(accessToken)) {
            log.warn("保存access_token失败：参数不能为空");
            return;
        }
        
        String cacheKey = WeworkConstants.CacheKey.ACCESS_TOKEN_PREFIX + corpId + ":" + agentId;
        // 提前5分钟过期，避免使用过期token
        int cacheTime = Math.max(expiresIn - 300, 60);
        redisCache.setCacheObject(cacheKey, accessToken, cacheTime, TimeUnit.SECONDS);
        
        log.debug("保存access_token成功，corpId: {}, agentId: {}, expiresIn: {}秒", corpId, agentId, expiresIn);
    }
    
    @Override
    public String getAccessToken(String corpId, String agentId) {
        if (StringUtils.isEmpty(corpId) || StringUtils.isEmpty(agentId)) {
            log.warn("获取access_token失败：参数不能为空");
            return null;
        }
        
        String cacheKey = WeworkConstants.CacheKey.ACCESS_TOKEN_PREFIX + corpId + ":" + agentId;
        String accessToken = redisCache.getCacheObject(cacheKey);
        
        if (StringUtils.isEmpty(accessToken)) {
            log.warn("获取access_token失败：缓存中不存在或已过期，corpId: {}, agentId: {}", corpId, agentId);
        } else {
            log.debug("获取access_token成功，corpId: {}, agentId: {}", corpId, agentId);
        }
        
        return accessToken;
    }
    
    @Override
    public void saveAuthInfo(String corpId, String authInfo, int expiresIn) {
        if (StringUtils.isEmpty(corpId) || StringUtils.isEmpty(authInfo)) {
            log.warn("保存授权信息失败：参数不能为空");
            return;
        }
        
        String cacheKey = WeworkConstants.CacheKey.AUTH_INFO_PREFIX + corpId;
        redisCache.setCacheObject(cacheKey, authInfo, expiresIn, TimeUnit.SECONDS);
        
        log.debug("保存授权信息成功，corpId: {}, expiresIn: {}秒", corpId, expiresIn);
    }
    
    @Override
    public String getAuthInfo(String corpId) {
        if (StringUtils.isEmpty(corpId)) {
            log.warn("获取授权信息失败：corpId不能为空");
            return null;
        }
        
        String cacheKey = WeworkConstants.CacheKey.AUTH_INFO_PREFIX + corpId;
        String authInfo = redisCache.getCacheObject(cacheKey);
        
        if (StringUtils.isEmpty(authInfo)) {
            log.warn("获取授权信息失败：缓存中不存在或已过期，corpId: {}", corpId);
        } else {
            log.debug("获取授权信息成功，corpId: {}", corpId);
        }
        
        return authInfo;
    }
    
    @Override
    public void removeCorpCache(String corpId) {
        if (StringUtils.isEmpty(corpId)) {
            log.warn("删除企业缓存失败：corpId不能为空");
            return;
        }
        
        // 删除授权信息缓存
        String authInfoKey = WeworkConstants.CacheKey.AUTH_INFO_PREFIX + corpId;
        redisCache.deleteObject(authInfoKey);
        
        // 删除access_token缓存（需要遍历所有可能的agentId，这里简化处理）
        // 实际使用中可以维护一个corpId对应的agentId列表
        
        log.debug("删除企业缓存成功，corpId: {}", corpId);
    }
}
