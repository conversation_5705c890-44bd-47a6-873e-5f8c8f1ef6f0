package com.ems.wework.service;

/**
 * 企业微信加解密服务接口
 * 
 * <AUTHOR>
 */
public interface IWeworkCryptoService {
    
    /**
     * 验证回调URL
     * 用于企业微信首次配置回调URL时的验证
     * 
     * @param msgSignature 签名
     * @param timestamp 时间戳
     * @param nonce 随机数
     * @param echostr 随机字符串
     * @return 解密后的echostr
     */
    String verifyUrl(String msgSignature, String timestamp, String nonce, String echostr);
    
    /**
     * 验证回调消息签名
     * 
     * @param msgSignature 消息签名
     * @param timestamp 时间戳
     * @param nonce 随机数
     * @param encryptData 加密数据
     * @return 验证结果
     */
    boolean verifySignature(String msgSignature, String timestamp, String nonce, String encryptData);
    
    /**
     * 解密回调消息
     *
     * @param encryptData 加密的消息数据
     * @return 解密后的消息内容
     */
    String decrypt(String encryptData);

    /**
     * 解密回调消息（包含签名验证）
     *
     * @param msgSignature 消息签名
     * @param timestamp 时间戳
     * @param nonce 随机数
     * @param postData POST数据
     * @return 解密后的消息
     */
    String decryptMsg(String msgSignature, String timestamp, String nonce, String postData);
    
    /**
     * 加密响应消息
     * 
     * @param responseMsg 响应消息内容
     * @return 加密后的响应数据
     */
    String encrypt(String responseMsg);
    
    /**
     * 生成回调响应
     * 
     * @param responseMsg 响应消息内容
     * @param timestamp 时间戳
     * @param nonce 随机数
     * @return 完整的加密响应（包含签名）
     */
    String generateResponse(String responseMsg, String timestamp, String nonce);
}
