package com.ems.wework.service;

/**
 * 企业微信回调服务接口
 * 
 * <AUTHOR>
 */
public interface IWeworkCallbackService {
    
    /**
     * 处理数据回调事件
     * 根据InfoType分发到不同的处理器
     * 
     * @param decryptedMsg 解密后的消息内容
     * @return 响应消息
     */
    String handleDataCallback(String decryptedMsg);
    
    /**
     * 处理指令回调事件
     * 处理用户交互事件（菜单点击、消息发送等）
     * 
     * @param decryptedMsg 解密后的消息内容
     * @return 响应消息
     */
    String handleCommandCallback(String decryptedMsg);
    
    /**
     * 处理suite_ticket推送
     * InfoType: suite_ticket
     * 
     * @param suiteTicketInfo suite_ticket信息
     * @return 处理结果
     */
    boolean handleSuiteTicket(String suiteTicketInfo);
    
    /**
     * 处理授权成功事件
     * InfoType: create_auth
     * 
     * @param authInfo 授权信息
     * @return 处理结果
     */
    boolean handleCreateAuth(String authInfo);
    
    /**
     * 处理授权变更事件
     * InfoType: change_auth
     * 
     * @param authInfo 授权变更信息
     * @return 处理结果
     */
    boolean handleChangeAuth(String authInfo);
    
    /**
     * 处理授权取消事件
     * InfoType: cancel_auth
     * 
     * @param authInfo 授权取消信息
     * @return 处理结果
     */
    boolean handleCancelAuth(String authInfo);
    
    /**
     * 处理通讯录变更事件
     * InfoType: change_contact
     * 
     * @param contactInfo 通讯录变更信息
     * @return 处理结果
     */
    boolean handleChangeContact(String contactInfo);
    
    /**
     * 处理共享应用事件
     * InfoType: share_chain_change
     * 
     * @param shareInfo 共享应用信息
     * @return 处理结果
     */
    boolean handleShareChainChange(String shareInfo);
    
    /**
     * 处理模板卡片事件
     * InfoType: template_card_event
     * 
     * @param cardInfo 模板卡片事件信息
     * @return 处理结果
     */
    boolean handleTemplateCardEvent(String cardInfo);
    
    /**
     * 处理模板卡片菜单事件
     * InfoType: template_card_menu_event
     * 
     * @param menuInfo 模板卡片菜单事件信息
     * @return 处理结果
     */
    boolean handleTemplateCardMenuEvent(String menuInfo);
    
    /**
     * 记录回调日志
     * 
     * @param msgType 消息类型
     * @param eventType 事件类型
     * @param infoType InfoType
     * @param fromUser 发送方用户
     * @param toUser 接收方用户
     * @param agentId 应用ID
     * @param msgContent 消息内容
     * @param responseContent 响应内容
     * @param processStatus 处理状态
     * @param errorMsg 错误信息
     * @param processTime 处理耗时
     * @param requestIp 请求IP
     * @param userAgent 用户代理
     */
    void logCallback(String msgType, String eventType, String infoType, String fromUser, 
                    String toUser, String agentId, String msgContent, String responseContent,
                    String processStatus, String errorMsg, Long processTime, 
                    String requestIp, String userAgent);
}
