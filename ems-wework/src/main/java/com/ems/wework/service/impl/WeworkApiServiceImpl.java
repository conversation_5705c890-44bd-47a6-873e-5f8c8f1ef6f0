package com.ems.wework.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.ems.common.utils.StringUtils;
import com.ems.wework.config.WeworkConfig;
import com.ems.wework.constants.WeworkConstants;
import com.ems.wework.domain.dto.WeworkMessage;
import com.ems.wework.domain.dto.WeworkUser;
import com.ems.wework.service.IWeworkApiService;
import com.ems.wework.service.IWeworkTicketService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.http.*;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.util.HashMap;
import java.util.Map;

/**
 * 企业微信API调用服务实现
 * 
 * <AUTHOR>
 */
@Service
public class WeworkApiServiceImpl implements IWeworkApiService {

    private static final Logger log = LoggerFactory.getLogger(WeworkApiServiceImpl.class);

    @Autowired
    private WeworkConfig weworkConfig;

    @Autowired
    private IWeworkTicketService ticketService;

    @Autowired
    @Qualifier("weworkRestTemplate")
    private RestTemplate restTemplate;

    @Override
    public String getSuiteAccessToken(String suiteId, String suiteSecret, String suiteTicket) {
        try {
            // 先从缓存获取
            String cachedToken = ticketService.getSuiteAccessToken(suiteId);
            if (StringUtils.isNotEmpty(cachedToken)) {
                return cachedToken;
            }

            // 构造请求参数
            Map<String, String> params = new HashMap<>();
            params.put("suite_id", suiteId);
            params.put("suite_secret", suiteSecret);
            params.put("suite_ticket", suiteTicket);

            String url = weworkConfig.getApi().getBaseUrl() + WeworkConstants.GET_SUITE_ACCESS_TOKEN_URL;

            // 发送请求
            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            HttpEntity<String> entity = new HttpEntity<>(JSON.toJSONString(params), headers);

            ResponseEntity<String> response = restTemplate.postForEntity(url, entity, String.class);

            if (response.getStatusCode() == HttpStatus.OK) {
                JSONObject result = JSON.parseObject(response.getBody());
                int errcode = result.getIntValue("errcode");

                if (errcode == 0) {
                    String suiteAccessToken = result.getString("suite_access_token");
                    int expiresIn = result.getIntValue("expires_in");

                    // 缓存token
                    ticketService.saveSuiteAccessToken(suiteId, suiteAccessToken, expiresIn);

                    log.info("获取suite_access_token成功：suiteId={}", suiteId);
                    return suiteAccessToken;
                } else {
                    String errmsg = result.getString("errmsg");
                    log.error("获取suite_access_token失败：errcode={}, errmsg={}", errcode, errmsg);
                    throw new RuntimeException("获取suite_access_token失败：" + errmsg);
                }
            } else {
                throw new RuntimeException("HTTP请求失败：" + response.getStatusCode());
            }

        } catch (Exception e) {
            log.error("获取suite_access_token异常", e);
            throw new RuntimeException("获取suite_access_token异常", e);
        }
    }

    @Override
    public String getPermanentCode(String suiteAccessToken, String authCode) {
        try {
            Map<String, String> params = new HashMap<>();
            params.put("auth_code", authCode);

            String url = weworkConfig.getApi().getBaseUrl() + WeworkConstants.GET_PERMANENT_CODE_URL
                    + "?suite_access_token=" + suiteAccessToken;

            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            HttpEntity<String> entity = new HttpEntity<>(JSON.toJSONString(params), headers);

            ResponseEntity<String> response = restTemplate.postForEntity(url, entity, String.class);

            if (response.getStatusCode() == HttpStatus.OK) {
                JSONObject result = JSON.parseObject(response.getBody());
                int errcode = result.getIntValue("errcode");

                if (errcode == 0) {
                    log.info("获取永久授权码成功");
                    return response.getBody();
                } else {
                    String errmsg = result.getString("errmsg");
                    log.error("获取永久授权码失败：errcode={}, errmsg={}", errcode, errmsg);
                    throw new RuntimeException("获取永久授权码失败：" + errmsg);
                }
            } else {
                throw new RuntimeException("HTTP请求失败：" + response.getStatusCode());
            }

        } catch (Exception e) {
            log.error("获取永久授权码异常", e);
            throw new RuntimeException("获取永久授权码异常", e);
        }
    }

    @Override
    public String getAuthInfo(String suiteAccessToken, String authCorpId, String permanentCode) {
        try {
            // 先从缓存获取
            String cachedAuthInfo = ticketService.getAuthInfo(authCorpId);
            if (StringUtils.isNotEmpty(cachedAuthInfo)) {
                return cachedAuthInfo;
            }

            Map<String, String> params = new HashMap<>();
            params.put("auth_corpid", authCorpId);
            params.put("permanent_code", permanentCode);

            String url = weworkConfig.getApi().getBaseUrl() + WeworkConstants.GET_AUTH_INFO_URL
                    + "?suite_access_token=" + suiteAccessToken;

            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            HttpEntity<String> entity = new HttpEntity<>(JSON.toJSONString(params), headers);

            ResponseEntity<String> response = restTemplate.postForEntity(url, entity, String.class);

            if (response.getStatusCode() == HttpStatus.OK) {
                JSONObject result = JSON.parseObject(response.getBody());
                int errcode = result.getIntValue("errcode");

                if (errcode == 0) {
                    // 缓存授权信息，有效期设置为1小时
                    ticketService.saveAuthInfo(authCorpId, response.getBody(), 3600);

                    log.info("获取企业授权信息成功：authCorpId={}", authCorpId);
                    return response.getBody();
                } else {
                    String errmsg = result.getString("errmsg");
                    log.error("获取企业授权信息失败：errcode={}, errmsg={}", errcode, errmsg);
                    throw new RuntimeException("获取企业授权信息失败：" + errmsg);
                }
            } else {
                throw new RuntimeException("HTTP请求失败：" + response.getStatusCode());
            }

        } catch (Exception e) {
            log.error("获取企业授权信息异常", e);
            throw new RuntimeException("获取企业授权信息异常", e);
        }
    }

    @Override
    public String getAccessToken(String corpId, String corpSecret) {
        try {
            // 先从缓存获取
            String cachedToken = ticketService.getAccessToken(corpId, "default");
            if (StringUtils.isNotEmpty(cachedToken)) {
                return cachedToken;
            }

            String url = weworkConfig.getApi().getBaseUrl() + WeworkConstants.GET_ACCESS_TOKEN_URL
                    + "?corpid=" + corpId + "&corpsecret=" + corpSecret;

            ResponseEntity<String> response = restTemplate.getForEntity(url, String.class);

            if (response.getStatusCode() == HttpStatus.OK) {
                JSONObject result = JSON.parseObject(response.getBody());
                int errcode = result.getIntValue("errcode");

                if (errcode == 0) {
                    String accessToken = result.getString("access_token");
                    int expiresIn = result.getIntValue("expires_in");

                    // 缓存token
                    ticketService.saveAccessToken(corpId, "default", accessToken, expiresIn);

                    log.info("获取access_token成功：corpId={}", corpId);
                    return accessToken;
                } else {
                    String errmsg = result.getString("errmsg");
                    log.error("获取access_token失败：errcode={}, errmsg={}", errcode, errmsg);
                    throw new RuntimeException("获取access_token失败：" + errmsg);
                }
            } else {
                throw new RuntimeException("HTTP请求失败：" + response.getStatusCode());
            }

        } catch (Exception e) {
            log.error("获取access_token异常", e);
            throw new RuntimeException("获取access_token异常", e);
        }
    }

    @Override
    public String getCorpAccessToken(String suiteAccessToken, String authCorpId, String permanentCode) {
        try {
            // 先从缓存获取
            String cachedToken = ticketService.getAccessToken(authCorpId, "suite");
            if (StringUtils.isNotEmpty(cachedToken)) {
                return cachedToken;
            }

            Map<String, String> params = new HashMap<>();
            params.put("auth_corpid", authCorpId);
            params.put("permanent_code", permanentCode);

            String url = weworkConfig.getApi().getBaseUrl() + "/cgi-bin/service/get_corp_token"
                    + "?suite_access_token=" + suiteAccessToken;

            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            HttpEntity<String> entity = new HttpEntity<>(JSON.toJSONString(params), headers);

            ResponseEntity<String> response = restTemplate.postForEntity(url, entity, String.class);

            if (response.getStatusCode() == HttpStatus.OK) {
                JSONObject result = JSON.parseObject(response.getBody());
                int errcode = result.getIntValue("errcode");

                if (errcode == 0) {
                    String accessToken = result.getString("access_token");
                    int expiresIn = result.getIntValue("expires_in");

                    // 缓存token
                    ticketService.saveAccessToken(authCorpId, "suite", accessToken, expiresIn);

                    log.info("获取企业access_token成功：authCorpId={}", authCorpId);
                    return accessToken;
                } else {
                    String errmsg = result.getString("errmsg");
                    log.error("获取企业access_token失败：errcode={}, errmsg={}", errcode, errmsg);
                    throw new RuntimeException("获取企业access_token失败：" + errmsg);
                }
            } else {
                throw new RuntimeException("HTTP请求失败：" + response.getStatusCode());
            }

        } catch (Exception e) {
            log.error("获取企业access_token异常", e);
            throw new RuntimeException("获取企业access_token异常", e);
        }
    }

    @Override
    public WeworkUser getUserInfoByCode(String accessToken, String code) {
        try {
            String url = weworkConfig.getApi().getBaseUrl() + WeworkConstants.GET_USER_INFO_URL
                    + "?access_token=" + accessToken + "&code=" + code;

            ResponseEntity<String> response = restTemplate.getForEntity(url, String.class);

            if (response.getStatusCode() == HttpStatus.OK) {
                JSONObject result = JSON.parseObject(response.getBody());
                int errcode = result.getIntValue("errcode");

                if (errcode == 0) {
                    String userId = result.getString("UserId");
                    if (StringUtils.isNotEmpty(userId)) {
                        // 如果有UserId，获取用户详细信息
                        return getUserDetail(accessToken, userId);
                    } else {
                        // 外部联系人或其他情况
                        WeworkUser user = new WeworkUser();
                        user.setUserId(result.getString("OpenId"));
                        user.setName("外部用户");
                        return user;
                    }
                } else {
                    String errmsg = result.getString("errmsg");
                    log.error("通过code获取用户信息失败：errcode={}, errmsg={}", errcode, errmsg);
                    throw new RuntimeException("通过code获取用户信息失败：" + errmsg);
                }
            } else {
                throw new RuntimeException("HTTP请求失败：" + response.getStatusCode());
            }

        } catch (Exception e) {
            log.error("通过code获取用户信息异常", e);
            throw new RuntimeException("通过code获取用户信息异常", e);
        }
    }

    @Override
    public WeworkUser getUserDetail(String accessToken, String userId) {
        try {
            String url = weworkConfig.getApi().getBaseUrl() + WeworkConstants.GET_USER_DETAIL_URL
                    + "?access_token=" + accessToken + "&userid=" + userId;

            ResponseEntity<String> response = restTemplate.getForEntity(url, String.class);

            if (response.getStatusCode() == HttpStatus.OK) {
                JSONObject result = JSON.parseObject(response.getBody());
                int errcode = result.getIntValue("errcode");

                if (errcode == 0) {
                    WeworkUser user = JSON.parseObject(result.toJSONString(), WeworkUser.class);
                    log.info("获取用户详细信息成功：userId={}", userId);
                    return user;
                } else {
                    String errmsg = result.getString("errmsg");
                    log.error("获取用户详细信息失败：errcode={}, errmsg={}", errcode, errmsg);
                    throw new RuntimeException("获取用户详细信息失败：" + errmsg);
                }
            } else {
                throw new RuntimeException("HTTP请求失败：" + response.getStatusCode());
            }

        } catch (Exception e) {
            log.error("获取用户详细信息异常", e);
            throw new RuntimeException("获取用户详细信息异常", e);
        }
    }

    @Override
    public String sendMessage(String accessToken, WeworkMessage message) {
        try {
            String url = weworkConfig.getApi().getBaseUrl() + WeworkConstants.SEND_MESSAGE_URL
                    + "?access_token=" + accessToken;

            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            HttpEntity<String> entity = new HttpEntity<>(JSON.toJSONString(message), headers);

            ResponseEntity<String> response = restTemplate.postForEntity(url, entity, String.class);

            if (response.getStatusCode() == HttpStatus.OK) {
                JSONObject result = JSON.parseObject(response.getBody());
                int errcode = result.getIntValue("errcode");

                if (errcode == 0) {
                    log.info("发送消息成功");
                    return response.getBody();
                } else {
                    String errmsg = result.getString("errmsg");
                    log.error("发送消息失败：errcode={}, errmsg={}", errcode, errmsg);
                    throw new RuntimeException("发送消息失败：" + errmsg);
                }
            } else {
                throw new RuntimeException("HTTP请求失败：" + response.getStatusCode());
            }

        } catch (Exception e) {
            log.error("发送消息异常", e);
            throw new RuntimeException("发送消息异常", e);
        }
    }

    @Override
    public String getDepartmentList(String accessToken, String deptId) {
        try {
            String url = weworkConfig.getApi().getBaseUrl() + "/cgi-bin/department/list"
                    + "?access_token=" + accessToken;

            if (StringUtils.isNotEmpty(deptId)) {
                url += "&id=" + deptId;
            }

            ResponseEntity<String> response = restTemplate.getForEntity(url, String.class);

            if (response.getStatusCode() == HttpStatus.OK) {
                JSONObject result = JSON.parseObject(response.getBody());
                int errcode = result.getIntValue("errcode");

                if (errcode == 0) {
                    log.info("获取部门列表成功");
                    return response.getBody();
                } else {
                    String errmsg = result.getString("errmsg");
                    log.error("获取部门列表失败：errcode={}, errmsg={}", errcode, errmsg);
                    throw new RuntimeException("获取部门列表失败：" + errmsg);
                }
            } else {
                throw new RuntimeException("HTTP请求失败：" + response.getStatusCode());
            }

        } catch (Exception e) {
            log.error("获取部门列表异常", e);
            throw new RuntimeException("获取部门列表异常", e);
        }
    }

    @Override
    public String getDepartmentUsers(String accessToken, String deptId, boolean fetchChild) {
        try {
            String url = weworkConfig.getApi().getBaseUrl() + "/cgi-bin/user/simplelist"
                    + "?access_token=" + accessToken + "&department_id=" + deptId
                    + "&fetch_child=" + (fetchChild ? 1 : 0);

            ResponseEntity<String> response = restTemplate.getForEntity(url, String.class);

            if (response.getStatusCode() == HttpStatus.OK) {
                JSONObject result = JSON.parseObject(response.getBody());
                int errcode = result.getIntValue("errcode");

                if (errcode == 0) {
                    log.info("获取部门成员成功：deptId={}", deptId);
                    return response.getBody();
                } else {
                    String errmsg = result.getString("errmsg");
                    log.error("获取部门成员失败：errcode={}, errmsg={}", errcode, errmsg);
                    throw new RuntimeException("获取部门成员失败：" + errmsg);
                }
            } else {
                throw new RuntimeException("HTTP请求失败：" + response.getStatusCode());
            }

        } catch (Exception e) {
            log.error("获取部门成员异常", e);
            throw new RuntimeException("获取部门成员异常", e);
        }
    }

    @Override
    public String createUser(String accessToken, String userInfo) {
        try {
            String url = weworkConfig.getApi().getBaseUrl() + "/cgi-bin/user/create"
                    + "?access_token=" + accessToken;

            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            HttpEntity<String> entity = new HttpEntity<>(userInfo, headers);

            ResponseEntity<String> response = restTemplate.postForEntity(url, entity, String.class);

            if (response.getStatusCode() == HttpStatus.OK) {
                JSONObject result = JSON.parseObject(response.getBody());
                int errcode = result.getIntValue("errcode");

                if (errcode == 0) {
                    log.info("创建成员成功");
                    return response.getBody();
                } else {
                    String errmsg = result.getString("errmsg");
                    log.error("创建成员失败：errcode={}, errmsg={}", errcode, errmsg);
                    throw new RuntimeException("创建成员失败：" + errmsg);
                }
            } else {
                throw new RuntimeException("HTTP请求失败：" + response.getStatusCode());
            }

        } catch (Exception e) {
            log.error("创建成员异常", e);
            throw new RuntimeException("创建成员异常", e);
        }
    }

    @Override
    public String updateUser(String accessToken, String userInfo) {
        try {
            String url = weworkConfig.getApi().getBaseUrl() + "/cgi-bin/user/update"
                    + "?access_token=" + accessToken;

            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            HttpEntity<String> entity = new HttpEntity<>(userInfo, headers);

            ResponseEntity<String> response = restTemplate.postForEntity(url, entity, String.class);

            if (response.getStatusCode() == HttpStatus.OK) {
                JSONObject result = JSON.parseObject(response.getBody());
                int errcode = result.getIntValue("errcode");

                if (errcode == 0) {
                    log.info("更新成员成功");
                    return response.getBody();
                } else {
                    String errmsg = result.getString("errmsg");
                    log.error("更新成员失败：errcode={}, errmsg={}", errcode, errmsg);
                    throw new RuntimeException("更新成员失败：" + errmsg);
                }
            } else {
                throw new RuntimeException("HTTP请求失败：" + response.getStatusCode());
            }

        } catch (Exception e) {
            log.error("更新成员异常", e);
            throw new RuntimeException("更新成员异常", e);
        }
    }

    @Override
    public String deleteUser(String accessToken, String userId) {
        try {
            String url = weworkConfig.getApi().getBaseUrl() + "/cgi-bin/user/delete"
                    + "?access_token=" + accessToken + "&userid=" + userId;

            ResponseEntity<String> response = restTemplate.getForEntity(url, String.class);

            if (response.getStatusCode() == HttpStatus.OK) {
                JSONObject result = JSON.parseObject(response.getBody());
                int errcode = result.getIntValue("errcode");

                if (errcode == 0) {
                    log.info("删除成员成功：userId={}", userId);
                    return response.getBody();
                } else {
                    String errmsg = result.getString("errmsg");
                    log.error("删除成员失败：errcode={}, errmsg={}", errcode, errmsg);
                    throw new RuntimeException("删除成员失败：" + errmsg);
                }
            } else {
                throw new RuntimeException("HTTP请求失败：" + response.getStatusCode());
            }

        } catch (Exception e) {
            log.error("删除成员异常", e);
            throw new RuntimeException("删除成员异常", e);
        }
    }

    @Override
    public String batchDeleteUsers(String accessToken, String[] userIds) {
        try {
            Map<String, String[]> params = new HashMap<>();
            params.put("useridlist", userIds);

            String url = weworkConfig.getApi().getBaseUrl() + "/cgi-bin/user/batchdelete"
                    + "?access_token=" + accessToken;

            HttpHeaders headers = new HttpHeaders();
            headers.setContentType(MediaType.APPLICATION_JSON);
            HttpEntity<String> entity = new HttpEntity<>(JSON.toJSONString(params), headers);

            ResponseEntity<String> response = restTemplate.postForEntity(url, entity, String.class);

            if (response.getStatusCode() == HttpStatus.OK) {
                JSONObject result = JSON.parseObject(response.getBody());
                int errcode = result.getIntValue("errcode");

                if (errcode == 0) {
                    log.info("批量删除成员成功：count={}", userIds.length);
                    return response.getBody();
                } else {
                    String errmsg = result.getString("errmsg");
                    log.error("批量删除成员失败：errcode={}, errmsg={}", errcode, errmsg);
                    throw new RuntimeException("批量删除成员失败：" + errmsg);
                }
            } else {
                throw new RuntimeException("HTTP请求失败：" + response.getStatusCode());
            }

        } catch (Exception e) {
            log.error("批量删除成员异常", e);
            throw new RuntimeException("批量删除成员异常", e);
        }
    }
}