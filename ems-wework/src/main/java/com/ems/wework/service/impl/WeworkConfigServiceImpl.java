package com.ems.wework.service.impl;

import com.ems.common.core.redis.RedisCache;
import com.ems.common.utils.DateUtils;
import com.ems.common.utils.StringUtils;
import com.ems.wework.domain.WeworkConfig;
import com.ems.wework.mapper.WeworkConfigMapper;
import com.ems.wework.service.IWeworkApiService;
import com.ems.wework.service.IWeworkConfigService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * 企业微信配置Service业务层处理
 * 
 * <AUTHOR>
 */
@Service
public class WeworkConfigServiceImpl implements IWeworkConfigService {
    
    private static final Logger log = LoggerFactory.getLogger(WeworkConfigServiceImpl.class);
    
    private static final String CONFIG_CACHE_KEY = "wework:config:current";
    
    @Autowired
    private WeworkConfigMapper weworkConfigMapper;
    
    @Autowired
    private IWeworkApiService apiService;
    
    @Autowired
    private RedisCache redisCache;

    /**
     * 查询企业微信配置
     * 
     * @param configId 企业微信配置主键
     * @return 企业微信配置
     */
    @Override
    public WeworkConfig selectWeworkConfigByConfigId(Long configId) {
        return weworkConfigMapper.selectWeworkConfigByConfigId(configId);
    }

    /**
     * 根据企业ID查询企业微信配置
     * 
     * @param corpId 企业ID
     * @return 企业微信配置
     */
    @Override
    public WeworkConfig selectWeworkConfigByCorpId(String corpId) {
        return weworkConfigMapper.selectWeworkConfigByCorpId(corpId);
    }

    /**
     * 查询企业微信配置列表
     * 
     * @param weworkConfig 企业微信配置
     * @return 企业微信配置
     */
    @Override
    public List<WeworkConfig> selectWeworkConfigList(WeworkConfig weworkConfig) {
        return weworkConfigMapper.selectWeworkConfigList(weworkConfig);
    }

    /**
     * 新增企业微信配置
     * 
     * @param weworkConfig 企业微信配置
     * @return 结果
     */
    @Override
    public int insertWeworkConfig(WeworkConfig weworkConfig) {
        weworkConfig.setCreateTime(DateUtils.getNowDate());
        int result = weworkConfigMapper.insertWeworkConfig(weworkConfig);
        
        // 刷新缓存
        if (result > 0) {
            refreshConfigCache();
        }
        
        return result;
    }

    /**
     * 修改企业微信配置
     * 
     * @param weworkConfig 企业微信配置
     * @return 结果
     */
    @Override
    public int updateWeworkConfig(WeworkConfig weworkConfig) {
        weworkConfig.setUpdateTime(DateUtils.getNowDate());
        int result = weworkConfigMapper.updateWeworkConfig(weworkConfig);
        
        // 刷新缓存
        if (result > 0) {
            refreshConfigCache();
        }
        
        return result;
    }

    /**
     * 批量删除企业微信配置
     * 
     * @param configIds 需要删除的企业微信配置主键
     * @return 结果
     */
    @Override
    public int deleteWeworkConfigByConfigIds(Long[] configIds) {
        int result = weworkConfigMapper.deleteWeworkConfigByConfigIds(configIds);
        
        // 刷新缓存
        if (result > 0) {
            refreshConfigCache();
        }
        
        return result;
    }

    /**
     * 删除企业微信配置信息
     * 
     * @param configId 企业微信配置主键
     * @return 结果
     */
    @Override
    public int deleteWeworkConfigByConfigId(Long configId) {
        int result = weworkConfigMapper.deleteWeworkConfigByConfigId(configId);
        
        // 刷新缓存
        if (result > 0) {
            refreshConfigCache();
        }
        
        return result;
    }

    /**
     * 测试企业微信连接
     * 
     * @param weworkConfig 企业微信配置
     * @return 测试结果
     */
    @Override
    public boolean testConnection(WeworkConfig weworkConfig) {
        try {
            if (StringUtils.isEmpty(weworkConfig.getCorpId()) || 
                StringUtils.isEmpty(weworkConfig.getCorpSecret())) {
                log.error("企业微信配置信息不完整");
                return false;
            }
            
            // 尝试获取access_token来测试连接
            String accessToken = apiService.getAccessToken(
                weworkConfig.getCorpId(), 
                weworkConfig.getCorpSecret()
            );
            
            boolean result = StringUtils.isNotEmpty(accessToken);
            log.info("企业微信连接测试结果：{}", result ? "成功" : "失败");
            
            return result;
            
        } catch (Exception e) {
            log.error("企业微信连接测试异常", e);
            return false;
        }
    }

    /**
     * 获取当前生效的企业微信配置
     * 
     * @return 企业微信配置
     */
    @Override
    public WeworkConfig getCurrentConfig() {
        // 先从缓存获取
        WeworkConfig cachedConfig = redisCache.getCacheObject(CONFIG_CACHE_KEY);
        if (cachedConfig != null) {
            return cachedConfig;
        }
        
        // 从数据库查询第一个正常状态的配置
        WeworkConfig queryConfig = new WeworkConfig();
        queryConfig.setStatus("1"); // 正常状态
        
        List<WeworkConfig> configList = selectWeworkConfigList(queryConfig);
        if (configList != null && !configList.isEmpty()) {
            WeworkConfig config = configList.get(0);
            
            // 缓存配置，有效期1小时
            redisCache.setCacheObject(CONFIG_CACHE_KEY, config, 1, TimeUnit.HOURS);
            
            return config;
        }
        
        return null;
    }

    /**
     * 刷新企业微信配置缓存
     */
    @Override
    public void refreshConfigCache() {
        try {
            // 删除缓存
            redisCache.deleteObject(CONFIG_CACHE_KEY);
            
            // 重新加载配置到缓存
            getCurrentConfig();
            
            log.info("企业微信配置缓存刷新成功");
            
        } catch (Exception e) {
            log.error("刷新企业微信配置缓存失败", e);
        }
    }
}
