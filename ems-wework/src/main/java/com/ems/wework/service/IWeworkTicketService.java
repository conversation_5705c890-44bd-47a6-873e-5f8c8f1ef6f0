package com.ems.wework.service;

/**
 * 企业微信第三方应用票据服务接口
 * 
 * <AUTHOR>
 */
public interface IWeworkTicketService {
    
    /**
     * 保存suite_ticket
     * 
     * @param suiteId 第三方应用ID
     * @param suiteTicket 第三方应用票据
     */
    void saveSuiteTicket(String suiteId, String suiteTicket);
    
    /**
     * 获取suite_ticket
     * 
     * @param suiteId 第三方应用ID
     * @return suite_ticket
     */
    String getSuiteTicket(String suiteId);
    
    /**
     * 保存suite_access_token
     * 
     * @param suiteId 第三方应用ID
     * @param suiteAccessToken 第三方应用访问令牌
     * @param expiresIn 过期时间（秒）
     */
    void saveSuiteAccessToken(String suiteId, String suiteAccessToken, int expiresIn);
    
    /**
     * 获取suite_access_token
     * 
     * @param suiteId 第三方应用ID
     * @return suite_access_token
     */
    String getSuiteAccessToken(String suiteId);
    
    /**
     * 保存企业access_token
     * 
     * @param corpId 企业ID
     * @param agentId 应用ID
     * @param accessToken 企业访问令牌
     * @param expiresIn 过期时间（秒）
     */
    void saveAccessToken(String corpId, String agentId, String accessToken, int expiresIn);
    
    /**
     * 获取企业access_token
     * 
     * @param corpId 企业ID
     * @param agentId 应用ID
     * @return access_token
     */
    String getAccessToken(String corpId, String agentId);
    
    /**
     * 保存企业授权信息
     * 
     * @param corpId 企业ID
     * @param authInfo 授权信息（JSON格式）
     * @param expiresIn 过期时间（秒）
     */
    void saveAuthInfo(String corpId, String authInfo, int expiresIn);
    
    /**
     * 获取企业授权信息
     * 
     * @param corpId 企业ID
     * @return 授权信息
     */
    String getAuthInfo(String corpId);
    
    /**
     * 删除企业相关缓存
     * 
     * @param corpId 企业ID
     */
    void removeCorpCache(String corpId);
}
