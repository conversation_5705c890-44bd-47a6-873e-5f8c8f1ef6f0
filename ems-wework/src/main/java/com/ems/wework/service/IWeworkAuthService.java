package com.ems.wework.service;

import com.ems.common.core.domain.entity.SysUser;
import com.ems.wework.domain.dto.WeworkUser;

/**
 * 企业微信认证服务接口
 * 
 * <AUTHOR>
 */
public interface IWeworkAuthService {
    
    /**
     * 构建企业微信授权URL
     * 
     * @param redirectUri 回调地址
     * @param state 状态参数（可选）
     * @return 授权URL
     */
    String buildAuthUrl(String redirectUri, String state);
    
    /**
     * 构建企业微信授权URL（使用默认配置）
     * 
     * @param redirectUri 回调地址
     * @return 授权URL
     */
    String buildAuthUrl(String redirectUri);
    
    /**
     * 企业微信登录
     * 
     * @param code 授权码
     * @return JWT Token
     */
    String login(String code);
    
    /**
     * 企业微信登录（指定企业和应用）
     * 
     * @param code 授权码
     * @param corpId 企业ID
     * @param agentId 应用ID
     * @return JWT Token
     */
    String login(String code, String corpId, String agentId);
    
    /**
     * 通过企业微信用户信息获取或创建本地用户
     * 
     * @param weworkUser 企业微信用户信息
     * @return 本地用户信息
     */
    SysUser getOrCreateUser(WeworkUser weworkUser);
    
    /**
     * 绑定企业微信用户到本地用户
     * 
     * @param userId 本地用户ID
     * @param weworkUser 企业微信用户信息
     * @return 绑定结果
     */
    boolean bindWeworkUser(Long userId, WeworkUser weworkUser);
    
    /**
     * 解绑企业微信用户
     * 
     * @param userId 本地用户ID
     * @return 解绑结果
     */
    boolean unbindWeworkUser(Long userId);
    
    /**
     * 根据企业微信用户ID查询本地用户
     * 
     * @param weworkUserId 企业微信用户ID
     * @return 本地用户信息
     */
    SysUser getUserByWeworkId(String weworkUserId);
    
    /**
     * 同步企业微信用户信息到本地
     * 
     * @param weworkUser 企业微信用户信息
     * @return 同步结果
     */
    boolean syncWeworkUser(WeworkUser weworkUser);
    
    /**
     * 验证企业微信登录是否启用
     * 
     * @return 是否启用
     */
    boolean isWeworkLoginEnabled();
    
    /**
     * 验证企业微信用户状态
     * 
     * @param weworkUser 企业微信用户信息
     * @return 是否有效
     */
    boolean isValidWeworkUser(WeworkUser weworkUser);
}
