package com.ems.wework.controller;

import com.ems.common.constant.Constants;
import com.ems.common.core.controller.BaseController;
import com.ems.common.core.domain.AjaxResult;
import com.ems.common.utils.StringUtils;
import com.ems.wework.domain.dto.WeworkLoginBody;
import com.ems.wework.service.IWeworkAuthService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.*;

/**
 * 企业微信认证控制器
 * 
 * <AUTHOR>
 */
@RestController
@RequestMapping("/wework/auth")
public class WeworkAuthController extends BaseController {
    
    @Autowired
    private IWeworkAuthService authService;
    
    /**
     * 获取企业微信授权URL
     * 
     * @param redirectUri 回调地址
     * @param state 状态参数（可选）
     * @return 授权URL
     */
    @GetMapping("/url")
    public AjaxResult getAuthUrl(@RequestParam String redirectUri, 
                                @RequestParam(required = false) String state) {
        try {
            if (StringUtils.isEmpty(redirectUri)) {
                return AjaxResult.error("回调地址不能为空");
            }
            
            if (!authService.isWeworkLoginEnabled()) {
                return AjaxResult.error("企业微信登录未启用");
            }
            
            String authUrl = authService.buildAuthUrl(redirectUri, state);
            return AjaxResult.success("获取授权URL成功").put("authUrl", authUrl);
            
        } catch (Exception e) {
            logger.error("获取企业微信授权URL失败", e);
            return AjaxResult.error("获取授权URL失败：" + e.getMessage());
        }
    }
    
    /**
     * 企业微信授权登录
     * 
     * @param loginBody 登录请求体
     * @return 登录结果
     */
    @PostMapping("/login")
    public AjaxResult weworkLogin(@RequestBody WeworkLoginBody loginBody) {
        try {
            if (StringUtils.isEmpty(loginBody.getCode())) {
                return AjaxResult.error("授权码不能为空");
            }
            
            if (!authService.isWeworkLoginEnabled()) {
                return AjaxResult.error("企业微信登录未启用");
            }
            
            // 执行登录
            String token;
            if (StringUtils.isNotEmpty(loginBody.getCorpId()) && 
                StringUtils.isNotEmpty(loginBody.getAgentId())) {
                // 指定企业和应用登录
                token = authService.login(loginBody.getCode(), 
                                        loginBody.getCorpId(), 
                                        loginBody.getAgentId());
            } else {
                // 使用默认配置登录
                token = authService.login(loginBody.getCode());
            }
            
            AjaxResult ajax = AjaxResult.success("登录成功");
            ajax.put(Constants.TOKEN, token);
            return ajax;
            
        } catch (Exception e) {
            logger.error("企业微信登录失败", e);
            return AjaxResult.error("登录失败：" + e.getMessage());
        }
    }
    
    /**
     * 绑定企业微信用户
     * 
     * @param code 授权码
     * @return 绑定结果
     */
    @PostMapping("/bind")
    public AjaxResult bindWeworkUser(@RequestParam String code) {
        try {
            if (StringUtils.isEmpty(code)) {
                return AjaxResult.error("授权码不能为空");
            }
            
            // TODO: 实现绑定逻辑
            // 1. 获取当前登录用户
            // 2. 通过code获取企业微信用户信息
            // 3. 绑定到当前用户
            
            return AjaxResult.success("绑定成功");
            
        } catch (Exception e) {
            logger.error("绑定企业微信用户失败", e);
            return AjaxResult.error("绑定失败：" + e.getMessage());
        }
    }
    
    /**
     * 解绑企业微信用户
     * 
     * @return 解绑结果
     */
    @PostMapping("/unbind")
    public AjaxResult unbindWeworkUser() {
        try {
            // TODO: 实现解绑逻辑
            // 1. 获取当前登录用户
            // 2. 解绑企业微信信息
            
            return AjaxResult.success("解绑成功");
            
        } catch (Exception e) {
            logger.error("解绑企业微信用户失败", e);
            return AjaxResult.error("解绑失败：" + e.getMessage());
        }
    }
    
    /**
     * 获取企业微信登录状态
     * 
     * @return 登录状态
     */
    @GetMapping("/status")
    public AjaxResult getWeworkStatus() {
        try {
            boolean enabled = authService.isWeworkLoginEnabled();
            return AjaxResult.success()
                    .put("enabled", enabled)
                    .put("message", enabled ? "企业微信登录已启用" : "企业微信登录未启用");
            
        } catch (Exception e) {
            logger.error("获取企业微信登录状态失败", e);
            return AjaxResult.error("获取状态失败：" + e.getMessage());
        }
    }
}
