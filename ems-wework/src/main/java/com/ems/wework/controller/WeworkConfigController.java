package com.ems.wework.controller;

import com.ems.common.annotation.Log;
import com.ems.common.core.controller.BaseController;
import com.ems.common.core.domain.AjaxResult;
import com.ems.common.core.page.TableDataInfo;
import com.ems.common.enums.BusinessType;
import com.ems.common.utils.poi.ExcelUtil;
import com.ems.wework.domain.WeworkConfig;
import com.ems.wework.service.IWeworkConfigService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.List;

/**
 * 企业微信配置Controller
 * 
 * <AUTHOR>
 */
@RestController
@RequestMapping("/wework/config")
public class WeworkConfigController extends BaseController {
    
    @Autowired
    private IWeworkConfigService weworkConfigService;

    /**
     * 查询企业微信配置列表
     */
    @PreAuthorize("@ss.hasPermi('wework:config:list')")
    @GetMapping("/list")
    public TableDataInfo list(WeworkConfig weworkConfig) {
        startPage();
        List<WeworkConfig> list = weworkConfigService.selectWeworkConfigList(weworkConfig);
        return getDataTable(list);
    }

    /**
     * 导出企业微信配置列表
     */
    @PreAuthorize("@ss.hasPermi('wework:config:export')")
    @Log(title = "企业微信配置", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public void export(HttpServletResponse response, WeworkConfig weworkConfig) {
        List<WeworkConfig> list = weworkConfigService.selectWeworkConfigList(weworkConfig);
        ExcelUtil<WeworkConfig> util = new ExcelUtil<WeworkConfig>(WeworkConfig.class);
        util.exportExcel(response, list, "企业微信配置数据");
    }

    /**
     * 获取企业微信配置详细信息
     */
    @PreAuthorize("@ss.hasPermi('wework:config:query')")
    @GetMapping(value = "/{configId}")
    public AjaxResult getInfo(@PathVariable("configId") Long configId) {
        return AjaxResult.success(weworkConfigService.selectWeworkConfigByConfigId(configId));
    }

    /**
     * 新增企业微信配置
     */
    @PreAuthorize("@ss.hasPermi('wework:config:add')")
    @Log(title = "企业微信配置", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody WeworkConfig weworkConfig) {
        return toAjax(weworkConfigService.insertWeworkConfig(weworkConfig));
    }

    /**
     * 修改企业微信配置
     */
    @PreAuthorize("@ss.hasPermi('wework:config:edit')")
    @Log(title = "企业微信配置", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody WeworkConfig weworkConfig) {
        return toAjax(weworkConfigService.updateWeworkConfig(weworkConfig));
    }

    /**
     * 删除企业微信配置
     */
    @PreAuthorize("@ss.hasPermi('wework:config:remove')")
    @Log(title = "企业微信配置", businessType = BusinessType.DELETE)
    @DeleteMapping("/{configIds}")
    public AjaxResult remove(@PathVariable Long[] configIds) {
        return toAjax(weworkConfigService.deleteWeworkConfigByConfigIds(configIds));
    }

    /**
     * 测试企业微信连接
     */
    @PreAuthorize("@ss.hasPermi('wework:config:test')")
    @PostMapping("/test")
    public AjaxResult testConnection(@RequestBody WeworkConfig weworkConfig) {
        try {
            boolean result = weworkConfigService.testConnection(weworkConfig);
            if (result) {
                return AjaxResult.success("连接测试成功");
            } else {
                return AjaxResult.error("连接测试失败");
            }
        } catch (Exception e) {
            logger.error("企业微信连接测试失败", e);
            return AjaxResult.error("连接测试失败：" + e.getMessage());
        }
    }

    /**
     * 获取当前生效的企业微信配置
     */
    @PreAuthorize("@ss.hasPermi('wework:config:query')")
    @GetMapping("/current")
    public AjaxResult getCurrentConfig() {
        try {
            WeworkConfig config = weworkConfigService.getCurrentConfig();
            if (config != null) {
                // 隐藏敏感信息
                config.setCorpSecret("******");
                config.setCallbackToken("******");
                config.setCallbackAesKey("******");
            }
            return AjaxResult.success(config);
        } catch (Exception e) {
            logger.error("获取当前企业微信配置失败", e);
            return AjaxResult.error("获取配置失败：" + e.getMessage());
        }
    }
}
