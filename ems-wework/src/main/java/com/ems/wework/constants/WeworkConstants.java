package com.ems.wework.constants;

/**
 * 企业微信常量定义
 * 
 * <AUTHOR>
 */
public class WeworkConstants {
    
    /**
     * 企业微信API基础URL
     */
    public static final String WEWORK_API_BASE_URL = "https://qyapi.weixin.qq.com";
    
    /**
     * 获取第三方应用凭证接口
     */
    public static final String GET_SUITE_ACCESS_TOKEN_URL = "/cgi-bin/service/get_suite_token";

    /**
     * 获取企业永久授权码接口
     */
    public static final String GET_PERMANENT_CODE_URL = "/cgi-bin/service/get_permanent_code";

    /**
     * 获取企业授权信息接口
     */
    public static final String GET_AUTH_INFO_URL = "/cgi-bin/service/get_auth_info";

    /**
     * 获取access_token接口
     */
    public static final String GET_ACCESS_TOKEN_URL = "/cgi-bin/gettoken";

    /**
     * 获取用户信息接口
     */
    public static final String GET_USER_INFO_URL = "/cgi-bin/user/getuserinfo";

    /**
     * 获取用户详细信息接口
     */
    public static final String GET_USER_DETAIL_URL = "/cgi-bin/user/get";

    /**
     * 发送应用消息接口
     */
    public static final String SEND_MESSAGE_URL = "/cgi-bin/message/send";
    
    /**
     * OAuth2授权基础URL
     */
    public static final String OAUTH2_AUTHORIZE_URL = "https://open.weixin.qq.com/connect/oauth2/authorize";
    
    /**
     * 企业微信登录授权scope
     */
    public static final String OAUTH2_SCOPE_SNSAPI_BASE = "snsapi_base";
    
    /**
     * 回调消息类型
     */
    public static class MsgType {
        /** 事件消息 */
        public static final String EVENT = "event";
        /** 文本消息 */
        public static final String TEXT = "text";
        /** 图片消息 */
        public static final String IMAGE = "image";
        /** 语音消息 */
        public static final String VOICE = "voice";
        /** 视频消息 */
        public static final String VIDEO = "video";
        /** 文件消息 */
        public static final String FILE = "file";
        /** 位置消息 */
        public static final String LOCATION = "location";
    }
    
    /**
     * 回调事件类型
     */
    public static class EventType {
        /** 订阅事件 */
        public static final String SUBSCRIBE = "subscribe";
        /** 取消订阅事件 */
        public static final String UNSUBSCRIBE = "unsubscribe";
        /** 菜单点击事件 */
        public static final String CLICK = "click";
        /** 菜单跳转事件 */
        public static final String VIEW = "view";
        /** 进入应用事件 */
        public static final String ENTER_AGENT = "enter_agent";
        /** 上报地理位置事件 */
        public static final String LOCATION = "location";
    }
    
    /**
     * 数据回调InfoType类型
     */
    public static class InfoType {
        /** 推送suite_ticket */
        public static final String SUITE_TICKET = "suite_ticket";
        /** 授权成功 */
        public static final String CREATE_AUTH = "create_auth";
        /** 授权变更 */
        public static final String CHANGE_AUTH = "change_auth";
        /** 授权取消 */
        public static final String CANCEL_AUTH = "cancel_auth";
        /** 通讯录变更 */
        public static final String CHANGE_CONTACT = "change_contact";
        /** 共享应用事件 */
        public static final String SHARE_CHAIN_CHANGE = "share_chain_change";
        /** 模板卡片事件 */
        public static final String TEMPLATE_CARD_EVENT = "template_card_event";
        /** 模板卡片菜单事件 */
        public static final String TEMPLATE_CARD_MENU_EVENT = "template_card_menu_event";
    }
    
    /**
     * 通讯录变更类型
     */
    public static class ChangeType {
        /** 新增成员 */
        public static final String CREATE_USER = "create_user";
        /** 更新成员 */
        public static final String UPDATE_USER = "update_user";
        /** 删除成员 */
        public static final String DELETE_USER = "delete_user";
        /** 新增部门 */
        public static final String CREATE_PARTY = "create_party";
        /** 更新部门 */
        public static final String UPDATE_PARTY = "update_party";
        /** 删除部门 */
        public static final String DELETE_PARTY = "delete_party";
        /** 标签成员变更 */
        public static final String UPDATE_TAG = "update_tag";
    }
    
    /**
     * 缓存键前缀
     */
    public static class CacheKey {
        /** suite_ticket缓存键前缀 */
        public static final String SUITE_TICKET_PREFIX = "wework:suite_ticket:";
        /** suite_access_token缓存键前缀 */
        public static final String SUITE_ACCESS_TOKEN_PREFIX = "wework:suite_access_token:";
        /** access_token缓存键前缀 */
        public static final String ACCESS_TOKEN_PREFIX = "wework:access_token:";
        /** 用户信息缓存键前缀 */
        public static final String USER_INFO_PREFIX = "wework:user_info:";
        /** 企业授权信息缓存键前缀 */
        public static final String AUTH_INFO_PREFIX = "wework:auth_info:";
    }
    
    /**
     * 配置键名
     */
    public static class ConfigKey {
        /** 企业ID */
        public static final String CORP_ID = "wework.corp.id";
        /** 应用密钥 */
        public static final String CORP_SECRET = "wework.corp.secret";
        /** 应用ID */
        public static final String AGENT_ID = "wework.agent.id";
        /** 回调Token */
        public static final String CALLBACK_TOKEN = "wework.callback.token";
        /** 回调加密密钥 */
        public static final String CALLBACK_AES_KEY = "wework.callback.aes.key";
        /** 登录开关 */
        public static final String LOGIN_ENABLED = "wework.login.enabled";
    }
    
    /**
     * HTTP状态码
     */
    public static class HttpStatus {
        /** 成功 */
        public static final int SUCCESS = 200;
        /** 签名验证失败 */
        public static final int SIGNATURE_ERROR = 40001;
        /** 解密失败 */
        public static final int DECRYPT_ERROR = 40002;
    }
    
    /**
     * 默认值
     */
    public static class Default {
        /** access_token默认过期时间（秒） */
        public static final int ACCESS_TOKEN_EXPIRE_TIME = 7200;
        /** 用户信息缓存时间（秒） */
        public static final int USER_INFO_CACHE_TIME = 3600;
        /** 回调处理超时时间（毫秒） */
        public static final int CALLBACK_TIMEOUT = 5000;
    }
}
