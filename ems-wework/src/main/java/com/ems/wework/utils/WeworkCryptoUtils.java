package com.ems.wework.utils;

import org.apache.commons.codec.binary.Base64;

import javax.crypto.Cipher;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.Charset;
import java.nio.charset.StandardCharsets;
import java.security.MessageDigest;
import java.util.Arrays;
import java.util.Random;

/**
 * 企业微信加解密工具类
 * 基于企业微信官方示例代码实现
 *
 * <AUTHOR>
 */
public class WeworkCryptoUtils {

    private static final String ALGORITHM = "AES";
    private static final String TRANSFORMATION = "AES/CBC/NoPadding";
    private static final Charset CHARSET = Charset.forName("utf-8");
    private static final int BLOCK_SIZE = 32;
    private static final Base64 base64 = new Base64();
    
    /**
     * 生成随机字符串（16位）
     *
     * @return 16位随机字符串
     */
    public static String generateRandomString() {
        String base = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";
        Random random = new Random();
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < 16; i++) {
            int number = random.nextInt(base.length());
            sb.append(base.charAt(number));
        }
        return sb.toString();
    }

    /**
     * 生成指定长度的随机字符串
     *
     * @param length 长度
     * @return 随机字符串
     */
    public static String generateRandomString(int length) {
        String base = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";
        Random random = new Random();
        StringBuilder sb = new StringBuilder();
        for (int i = 0; i < length; i++) {
            int number = random.nextInt(base.length());
            sb.append(base.charAt(number));
        }
        return sb.toString();
    }

    /**
     * 生成4个字节的网络字节序
     *
     * @param sourceNumber 源数字
     * @return 网络字节序
     */
    private static byte[] getNetworkBytesOrder(int sourceNumber) {
        byte[] orderBytes = new byte[4];
        orderBytes[3] = (byte) (sourceNumber & 0xFF);
        orderBytes[2] = (byte) (sourceNumber >> 8 & 0xFF);
        orderBytes[1] = (byte) (sourceNumber >> 16 & 0xFF);
        orderBytes[0] = (byte) (sourceNumber >> 24 & 0xFF);
        return orderBytes;
    }

    /**
     * 还原4个字节的网络字节序
     *
     * @param orderBytes 网络字节序
     * @return 源数字
     */
    private static int recoverNetworkBytesOrder(byte[] orderBytes) {
        int sourceNumber = 0;
        for (int i = 0; i < 4; i++) {
            sourceNumber <<= 8;
            sourceNumber |= orderBytes[i] & 0xff;
        }
        return sourceNumber;
    }

    /**
     * SHA1签名验证
     * 
     * @param token 企业微信Token
     * @param timestamp 时间戳
     * @param nonce 随机数
     * @param encrypt 加密数据
     * @return 签名字符串
     */
    public static String generateSignature(String token, String timestamp, String nonce, String encrypt) {
        try {
            String[] array = {token, timestamp, nonce, encrypt};
            Arrays.sort(array);
            
            StringBuilder sb = new StringBuilder();
            for (String str : array) {
                sb.append(str);
            }
            
            MessageDigest md = MessageDigest.getInstance("SHA-1");
            byte[] digest = md.digest(sb.toString().getBytes(StandardCharsets.UTF_8));
            
            StringBuilder hexString = new StringBuilder();
            for (byte b : digest) {
                String hex = Integer.toHexString(0xff & b);
                if (hex.length() == 1) {
                    hexString.append('0');
                }
                hexString.append(hex);
            }
            
            return hexString.toString();
        } catch (Exception e) {
            throw new RuntimeException("生成签名失败", e);
        }
    }
    
    /**
     * 验证签名
     * 
     * @param signature 待验证的签名
     * @param token 企业微信Token
     * @param timestamp 时间戳
     * @param nonce 随机数
     * @param encrypt 加密数据
     * @return 验证结果
     */
    public static boolean verifySignature(String signature, String token, String timestamp, String nonce, String encrypt) {
        String expectedSignature = generateSignature(token, timestamp, nonce, encrypt);
        return signature.equals(expectedSignature);
    }
    
    /**
     * AES加密
     *
     * @param plaintext 明文
     * @param corpId 企业ID
     * @param aesKey AES密钥
     * @return 加密后的Base64字符串
     */
    public static String encrypt(String plaintext, String corpId, String aesKey) {
        return encrypt(generateRandomString(), plaintext, corpId, aesKey);
    }

    /**
     * AES加密（指定随机字符串）
     *
     * @param randomStr 16位随机字符串
     * @param plaintext 明文
     * @param corpId 企业ID
     * @param aesKey AES密钥
     * @return 加密后的Base64字符串
     */
    public static String encrypt(String randomStr, String plaintext, String corpId, String aesKey) {
        try {
            ByteGroup byteCollector = new ByteGroup();
            byte[] randomStrBytes = randomStr.getBytes(CHARSET);
            byte[] textBytes = plaintext.getBytes(CHARSET);
            byte[] networkBytesOrder = getNetworkBytesOrder(textBytes.length);
            byte[] corpIdBytes = corpId.getBytes(CHARSET);

            // randomStr + networkBytesOrder + text + corpId
            byteCollector.addBytes(randomStrBytes);
            byteCollector.addBytes(networkBytesOrder);
            byteCollector.addBytes(textBytes);
            byteCollector.addBytes(corpIdBytes);

            // 使用PKCS7填充
            byte[] padBytes = PKCS7Encoder.encode(byteCollector.size());
            byteCollector.addBytes(padBytes);

            // 获得最终的字节流, 未加密
            byte[] unencrypted = byteCollector.toBytes();

            // 设置加密模式为AES的CBC模式
            Cipher cipher = Cipher.getInstance(TRANSFORMATION);
            byte[] keyBytes = Base64.decodeBase64(aesKey + "=");
            SecretKeySpec keySpec = new SecretKeySpec(keyBytes, ALGORITHM);
            IvParameterSpec iv = new IvParameterSpec(keyBytes, 0, 16);
            cipher.init(Cipher.ENCRYPT_MODE, keySpec, iv);

            // 加密
            byte[] encrypted = cipher.doFinal(unencrypted);

            // 使用BASE64对加密后的字符串进行编码
            return base64.encodeToString(encrypted);
        } catch (Exception e) {
            throw new RuntimeException("AES加密失败", e);
        }
    }
    
    /**
     * AES解密
     *
     * @param ciphertext 密文（Base64编码）
     * @param corpId 企业ID
     * @param aesKey AES密钥
     * @return 解密后的明文
     */
    public static String decrypt(String ciphertext, String corpId, String aesKey) {
        byte[] original;
        try {
            // 设置解密模式为AES的CBC模式
            Cipher cipher = Cipher.getInstance(TRANSFORMATION);
            byte[] keyBytes = Base64.decodeBase64(aesKey + "=");
            SecretKeySpec keySpec = new SecretKeySpec(keyBytes, ALGORITHM);
            IvParameterSpec iv = new IvParameterSpec(Arrays.copyOfRange(keyBytes, 0, 16));
            cipher.init(Cipher.DECRYPT_MODE, keySpec, iv);

            // 使用BASE64对密文进行解码
            byte[] encrypted = Base64.decodeBase64(ciphertext);

            // 解密
            original = cipher.doFinal(encrypted);
        } catch (Exception e) {
            throw new RuntimeException("AES解密失败", e);
        }

        String xmlContent, fromCorpId;
        try {
            // 去除补位字符
            byte[] bytes = PKCS7Encoder.decode(original);

            // 分离16位随机字符串,网络字节序和corpId
            byte[] networkOrder = Arrays.copyOfRange(bytes, 16, 20);

            int xmlLength = recoverNetworkBytesOrder(networkOrder);

            xmlContent = new String(Arrays.copyOfRange(bytes, 20, 20 + xmlLength), CHARSET);
            fromCorpId = new String(Arrays.copyOfRange(bytes, 20 + xmlLength, bytes.length), CHARSET);
        } catch (Exception e) {
            throw new RuntimeException("解密数据格式错误", e);
        }

        // corpId不相同的情况
        if (!fromCorpId.equals(corpId)) {
            throw new RuntimeException("企业ID验证失败，期望: " + corpId + ", 实际: " + fromCorpId);
        }

        return xmlContent;
    }
    
    /**
     * 字节组处理类
     */
    static class ByteGroup {
        private java.util.List<Byte> byteContainer = new java.util.ArrayList<>();

        public void addBytes(byte[] bytes) {
            for (byte b : bytes) {
                byteContainer.add(b);
            }
        }

        public byte[] toBytes() {
            byte[] bytes = new byte[byteContainer.size()];
            for (int i = 0; i < byteContainer.size(); i++) {
                bytes[i] = byteContainer.get(i);
            }
            return bytes;
        }

        public int size() {
            return byteContainer.size();
        }
    }

    /**
     * PKCS7编码器
     */
    static class PKCS7Encoder {
        private static final int BLOCK_SIZE = 32;

        /**
         * 获得对明文进行补位填充的字节
         *
         * @param count 需要进行填充补位操作的明文字节个数
         * @return 补齐用的字节数组
         */
        public static byte[] encode(int count) {
            // 计算需要填充的位数
            int amountToPad = BLOCK_SIZE - (count % BLOCK_SIZE);
            if (amountToPad == 0) {
                amountToPad = BLOCK_SIZE;
            }
            // 获得补位所用的字符
            char padChr = chr(amountToPad);
            String tmp = new String();
            for (int index = 0; index < amountToPad; index++) {
                tmp += padChr;
            }
            return tmp.getBytes(CHARSET);
        }

        /**
         * 删除解密后明文的补位字符
         *
         * @param decrypted 解密后的明文
         * @return 删除补位字符后的明文
         */
        public static byte[] decode(byte[] decrypted) {
            int pad = (int) decrypted[decrypted.length - 1];
            if (pad < 1 || pad > 32) {
                pad = 0;
            }
            return Arrays.copyOfRange(decrypted, 0, decrypted.length - pad);
        }

        /**
         * 将数字转化成ASCII码对应的字符，用于对明文进行补位
         *
         * @param a 需要转化的数字
         * @return 转化得到的字符
         */
        public static char chr(int a) {
            byte target = (byte) (a & 0xFF);
            return (char) target;
        }
    }
}
