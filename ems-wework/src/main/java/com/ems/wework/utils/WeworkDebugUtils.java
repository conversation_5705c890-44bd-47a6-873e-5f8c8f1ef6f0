package com.ems.wework.utils;

import org.apache.commons.codec.binary.Base64;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import javax.crypto.Cipher;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.Charset;
import java.util.Arrays;

/**
 * 企业微信调试工具类
 * 用于排查加解密问题
 * 
 * <AUTHOR>
 */
public class WeworkDebugUtils {
    
    private static final Logger log = LoggerFactory.getLogger(WeworkDebugUtils.class);
    private static final Charset CHARSET = Charset.forName("utf-8");
    
    /**
     * 调试解密过程，输出详细信息
     * 
     * @param encryptData 加密数据
     * @param token 回调Token
     * @param encodingAesKey AES密钥
     * @param expectedReceiverId 期望的接收者ID
     */
    public static void debugDecrypt(String encryptData, String token, String encodingAesKey, String expectedReceiverId) {
        log.info("=== 开始调试解密过程 ===");
        log.info("加密数据: {}", encryptData);
        log.info("Token: {}", token);
        log.info("EncodingAESKey: {}", encodingAesKey);
        log.info("期望的ReceiverId: {}", expectedReceiverId);
        
        try {
            // 1. 验证AES密钥格式
            if (encodingAesKey.length() != 43) {
                log.error("AES密钥长度错误，期望43位，实际{}位", encodingAesKey.length());
                return;
            }
            
            // 2. 解码AES密钥
            byte[] aesKey = Base64.decodeBase64(encodingAesKey + "=");
            log.info("AES密钥解码成功，长度: {}", aesKey.length);
            
            // 3. 解密数据
            byte[] original = decryptRaw(encryptData, aesKey);
            log.info("原始解密数据长度: {}", original.length);
            
            // 4. 去除PKCS7填充
            byte[] bytes = WeworkCryptoUtils.PKCS7Encoder.decode(original);
            log.info("去除填充后数据长度: {}", bytes.length);
            
            // 5. 解析数据结构
            if (bytes.length < 20) {
                log.error("解密数据长度不足，无法解析");
                return;
            }
            
            // 提取16位随机字符串
            String randomStr = new String(Arrays.copyOfRange(bytes, 0, 16), CHARSET);
            log.info("随机字符串: {}", randomStr);
            
            // 提取网络字节序（消息长度）
            byte[] networkOrder = Arrays.copyOfRange(bytes, 16, 20);
            int xmlLength = recoverNetworkBytesOrder(networkOrder);
            log.info("消息长度: {}", xmlLength);
            
            // 检查数据长度是否足够
            if (bytes.length < 20 + xmlLength) {
                log.error("数据长度不足，期望至少{}字节，实际{}字节", 20 + xmlLength, bytes.length);
                return;
            }
            
            // 提取消息内容
            String xmlContent = new String(Arrays.copyOfRange(bytes, 20, 20 + xmlLength), CHARSET);
            log.info("消息内容: {}", xmlContent);
            
            // 提取receiverId
            String actualReceiverId = new String(Arrays.copyOfRange(bytes, 20 + xmlLength, bytes.length), CHARSET);
            log.info("实际ReceiverId: {}", actualReceiverId);
            
            // 6. 比较receiverId
            if (!expectedReceiverId.equals(actualReceiverId)) {
                log.error("ReceiverId不匹配！");
                log.error("期望: {}", expectedReceiverId);
                log.error("实际: {}", actualReceiverId);
                log.error("期望长度: {}, 实际长度: {}", expectedReceiverId.length(), actualReceiverId.length());
                
                // 输出字节对比
                byte[] expectedBytes = expectedReceiverId.getBytes(CHARSET);
                byte[] actualBytes = actualReceiverId.getBytes(CHARSET);
                log.error("期望字节: {}", Arrays.toString(expectedBytes));
                log.error("实际字节: {}", Arrays.toString(actualBytes));
            } else {
                log.info("ReceiverId匹配成功！");
            }
            
        } catch (Exception e) {
            log.error("调试解密过程失败", e);
        }
        
        log.info("=== 调试解密过程结束 ===");
    }
    
    /**
     * 尝试用不同的receiverId解密
     * 
     * @param encryptData 加密数据
     * @param token 回调Token
     * @param encodingAesKey AES密钥
     * @param possibleReceiverIds 可能的接收者ID列表
     */
    public static void tryDecryptWithDifferentReceiverIds(String encryptData, String token, String encodingAesKey, String... possibleReceiverIds) {
        log.info("=== 尝试用不同的ReceiverId解密 ===");
        
        for (String receiverId : possibleReceiverIds) {
            try {
                log.info("尝试ReceiverId: {}", receiverId);
                WXBizMsgCrypt wxcpt = new WXBizMsgCrypt(token, encodingAesKey, receiverId);
                String result = wxcpt.decrypt(encryptData);
                log.info("✓ 解密成功！ReceiverId: {}, 结果: {}", receiverId, result);
                return;
            } catch (Exception e) {
                log.warn("✗ 解密失败，ReceiverId: {}, 错误: {}", receiverId, e.getMessage());
            }
        }
        
        log.error("所有ReceiverId都解密失败");
    }
    
    /**
     * 从加密XML中提取实际的receiverId
     * 
     * @param encryptData 加密数据
     * @param encodingAesKey AES密钥
     * @return 实际的receiverId，如果提取失败返回null
     */
    public static String extractActualReceiverId(String encryptData, String encodingAesKey) {
        try {
            byte[] aesKey = Base64.decodeBase64(encodingAesKey + "=");
            byte[] original = decryptRaw(encryptData, aesKey);
            byte[] bytes = WeworkCryptoUtils.PKCS7Encoder.decode(original);
            
            if (bytes.length < 20) {
                return null;
            }
            
            byte[] networkOrder = Arrays.copyOfRange(bytes, 16, 20);
            int xmlLength = recoverNetworkBytesOrder(networkOrder);
            
            if (bytes.length < 20 + xmlLength) {
                return null;
            }
            
            return new String(Arrays.copyOfRange(bytes, 20 + xmlLength, bytes.length), CHARSET);
        } catch (Exception e) {
            log.error("提取实际ReceiverId失败", e);
            return null;
        }
    }
    
    /**
     * 原始解密（不验证receiverId）
     */
    private static byte[] decryptRaw(String encryptData, byte[] aesKey) throws Exception {
        Cipher cipher = Cipher.getInstance("AES/CBC/NoPadding");
        SecretKeySpec keySpec = new SecretKeySpec(aesKey, "AES");
        IvParameterSpec iv = new IvParameterSpec(Arrays.copyOfRange(aesKey, 0, 16));
        cipher.init(Cipher.DECRYPT_MODE, keySpec, iv);
        
        byte[] encrypted = Base64.decodeBase64(encryptData);
        return cipher.doFinal(encrypted);
    }
    
    /**
     * 还原网络字节序
     */
    private static int recoverNetworkBytesOrder(byte[] orderBytes) {
        int sourceNumber = 0;
        for (int i = 0; i < 4; i++) {
            sourceNumber <<= 8;
            sourceNumber |= orderBytes[i] & 0xff;
        }
        return sourceNumber;
    }
    
    /**
     * 验证配置参数
     * 
     * @param token 回调Token
     * @param encodingAesKey AES密钥
     * @param receiverId 接收者ID
     */
    public static void validateConfig(String token, String encodingAesKey, String receiverId) {
        log.info("=== 验证配置参数 ===");
        
        // 验证Token
        if (token == null || token.trim().isEmpty()) {
            log.error("Token为空");
        } else {
            log.info("✓ Token: {} (长度: {})", token, token.length());
        }
        
        // 验证AES密钥
        if (encodingAesKey == null || encodingAesKey.length() != 43) {
            log.error("AES密钥格式错误，期望43位，实际: {}", encodingAesKey == null ? "null" : encodingAesKey.length());
        } else {
            log.info("✓ AES密钥长度正确: 43位");
        }
        
        // 验证ReceiverId
        if (receiverId == null || receiverId.trim().isEmpty()) {
            log.error("ReceiverId为空");
        } else {
            log.info("✓ ReceiverId: {} (长度: {})", receiverId, receiverId.length());
            
            // 检查是否是企业ID格式
            if (receiverId.startsWith("ww") && receiverId.length() == 18) {
                log.info("  格式：企业ID");
            } else if (receiverId.startsWith("ww") && receiverId.length() > 18) {
                log.info("  格式：可能是第三方应用ID");
            } else {
                log.warn("  格式：未知格式");
            }
        }
        
        log.info("=== 配置参数验证完成 ===");
    }
}
