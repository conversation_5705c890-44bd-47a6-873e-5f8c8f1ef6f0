package com.ems.wework.utils;

import org.apache.commons.codec.binary.Base64;

import javax.crypto.Cipher;
import javax.crypto.spec.IvParameterSpec;
import javax.crypto.spec.SecretKeySpec;
import java.nio.charset.Charset;
import java.util.Arrays;
import java.util.Random;

/**
 * 企业微信消息加解密类
 * 基于企业微信官方示例代码实现
 * 
 * <AUTHOR>
 */
public class WXBizMsgCrypt {
    
    static Charset CHARSET = Charset.forName("utf-8");
    Base64 base64 = new Base64();
    byte[] aesKey;
    String token;
    String receiveid;

    /**
     * 构造函数
     * 
     * @param token 企业微信后台，开发者设置的token
     * @param encodingAesKey 企业微信后台，开发者设置的EncodingAESKey
     * @param receiveid 不同场景含义不同，详见文档
     * @throws RuntimeException 执行失败
     */
    public WXBizMsgCrypt(String token, String encodingAesKey, String receiveid) throws RuntimeException {
        if (encodingAesKey.length() != 43) {
            throw new RuntimeException("非法的AES密钥长度");
        }

        this.token = token;
        this.receiveid = receiveid;
        aesKey = Base64.decodeBase64(encodingAesKey + "=");
    }

    /**
     * 生成4个字节的网络字节序
     */
    byte[] getNetworkBytesOrder(int sourceNumber) {
        byte[] orderBytes = new byte[4];
        orderBytes[3] = (byte) (sourceNumber & 0xFF);
        orderBytes[2] = (byte) (sourceNumber >> 8 & 0xFF);
        orderBytes[1] = (byte) (sourceNumber >> 16 & 0xFF);
        orderBytes[0] = (byte) (sourceNumber >> 24 & 0xFF);
        return orderBytes;
    }

    /**
     * 还原4个字节的网络字节序
     */
    int recoverNetworkBytesOrder(byte[] orderBytes) {
        int sourceNumber = 0;
        for (int i = 0; i < 4; i++) {
            sourceNumber <<= 8;
            sourceNumber |= orderBytes[i] & 0xff;
        }
        return sourceNumber;
    }

    /**
     * 随机生成16位字符串
     */
    public String getRandomStr() {
        String base = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789";
        Random random = new Random();
        StringBuffer sb = new StringBuffer();
        for (int i = 0; i < 16; i++) {
            int number = random.nextInt(base.length());
            sb.append(base.charAt(number));
        }
        return sb.toString();
    }

    /**
     * 对明文进行加密.
     * 
     * @param text 需要加密的明文
     * @return 加密后base64编码的字符串
     * @throws RuntimeException aes加密失败
     */
    public String encrypt(String randomStr, String text) throws RuntimeException {
        WeworkCryptoUtils.ByteGroup byteCollector = new WeworkCryptoUtils.ByteGroup();
        byte[] randomStrBytes = randomStr.getBytes(CHARSET);
        byte[] textBytes = text.getBytes(CHARSET);
        byte[] networkBytesOrder = getNetworkBytesOrder(textBytes.length);
        byte[] receiveidBytes = receiveid.getBytes(CHARSET);

        // randomStr + networkBytesOrder + text + receiveid
        byteCollector.addBytes(randomStrBytes);
        byteCollector.addBytes(networkBytesOrder);
        byteCollector.addBytes(textBytes);
        byteCollector.addBytes(receiveidBytes);

        // ... + pad: 使用自定义的填充方式对明文进行补位填充
        byte[] padBytes = WeworkCryptoUtils.PKCS7Encoder.encode(byteCollector.size());
        byteCollector.addBytes(padBytes);

        // 获得最终的字节流, 未加密
        byte[] unencrypted = byteCollector.toBytes();

        try {
            // 设置加密模式为AES的CBC模式
            Cipher cipher = Cipher.getInstance("AES/CBC/NoPadding");
            SecretKeySpec keySpec = new SecretKeySpec(aesKey, "AES");
            IvParameterSpec iv = new IvParameterSpec(aesKey, 0, 16);
            cipher.init(Cipher.ENCRYPT_MODE, keySpec, iv);

            // 加密
            byte[] encrypted = cipher.doFinal(unencrypted);

            // 使用BASE64对加密后的字符串进行编码
            String base64Encrypted = base64.encodeToString(encrypted);

            return base64Encrypted;
        } catch (Exception e) {
            e.printStackTrace();
            throw new RuntimeException("AES加密失败");
        }
    }

    /**
     * 对密文进行解密.
     * 
     * @param text 需要解密的密文
     * @return 解密得到的明文
     * @throws RuntimeException aes解密失败
     */
    public String decrypt(String text) throws RuntimeException {
        byte[] original;
        try {
            // 设置解密模式为AES的CBC模式
            Cipher cipher = Cipher.getInstance("AES/CBC/NoPadding");
            SecretKeySpec key_spec = new SecretKeySpec(aesKey, "AES");
            IvParameterSpec iv = new IvParameterSpec(Arrays.copyOfRange(aesKey, 0, 16));
            cipher.init(Cipher.DECRYPT_MODE, key_spec, iv);

            // 使用BASE64对密文进行解码
            byte[] encrypted = Base64.decodeBase64(text);

            // 解密
            original = cipher.doFinal(encrypted);
        } catch (Exception e) {
            e.printStackTrace();
            throw new RuntimeException("AES解密失败");
        }

        String xmlContent, from_receiveid;
        try {
            // 去除补位字符
            byte[] bytes = WeworkCryptoUtils.PKCS7Encoder.decode(original);

            // 分离16位随机字符串,网络字节序和receiveid
            byte[] networkOrder = Arrays.copyOfRange(bytes, 16, 20);

            int xmlLength = recoverNetworkBytesOrder(networkOrder);

            xmlContent = new String(Arrays.copyOfRange(bytes, 20, 20 + xmlLength), CHARSET);
            from_receiveid = new String(Arrays.copyOfRange(bytes, 20 + xmlLength, bytes.length), CHARSET);
        } catch (Exception e) {
            e.printStackTrace();
            throw new RuntimeException("解密数据格式错误");
        }

        // receiveid不相同的情况
        if (!from_receiveid.equals(receiveid)) {
            throw new RuntimeException("企业ID验证失败");
        }
        return xmlContent;
    }

    /**
     * 将企业微信回复用户的消息加密打包.
     * 
     * @param replyMsg 企业微信待回复用户的消息，xml格式的字符串
     * @param timeStamp 时间戳，可以自己生成，也可以用URL参数的timestamp
     * @param nonce 随机串，可以自己生成，也可以用URL参数的nonce
     * @return 加密后的可以直接回复用户的密文，包括msg_signature, timestamp, nonce, encrypt的xml格式的字符串
     * @throws RuntimeException 执行失败
     */
    public String EncryptMsg(String replyMsg, String timeStamp, String nonce) throws RuntimeException {
        // 加密
        String encrypt = encrypt(getRandomStr(), replyMsg);

        // 生成安全签名
        if (timeStamp.equals("")) {
            timeStamp = Long.toString(System.currentTimeMillis());
        }

        String signature = WeworkCryptoUtils.generateSignature(token, timeStamp, nonce, encrypt);

        // 生成发送的xml
        String result = WeworkXmlUtils.generateEncryptXml(encrypt, signature, timeStamp, nonce);
        return result;
    }

    /**
     * 检验消息的真实性，并且获取解密后的明文.
     * 
     * @param msgSignature 签名串，对应URL参数的msg_signature
     * @param timeStamp 时间戳，对应URL参数的timestamp
     * @param nonce 随机串，对应URL参数的nonce
     * @param postData 密文，对应POST请求的数据
     * @return 解密后的原文
     * @throws RuntimeException 执行失败
     */
    public String DecryptMsg(String msgSignature, String timeStamp, String nonce, String postData)
            throws RuntimeException {

        // 提取密文
        String encrypt = WeworkXmlUtils.extractEncryptData(postData);
        if (encrypt == null) {
            throw new RuntimeException("提取加密数据失败");
        }

        // 验证安全签名
        String signature = WeworkCryptoUtils.generateSignature(token, timeStamp, nonce, encrypt);

        // 和URL中的签名比较是否相等
        if (!signature.equals(msgSignature)) {
            throw new RuntimeException("签名验证失败");
        }

        // 解密
        String result = decrypt(encrypt);
        return result;
    }

    /**
     * 验证URL
     * 
     * @param msgSignature 签名串，对应URL参数的msg_signature
     * @param timeStamp 时间戳，对应URL参数的timestamp
     * @param nonce 随机串，对应URL参数的nonce
     * @param echoStr 随机串，对应URL参数的echostr
     * @return 解密之后的echostr
     * @throws RuntimeException 执行失败
     */
    public String VerifyURL(String msgSignature, String timeStamp, String nonce, String echoStr)
            throws RuntimeException {
        String signature = WeworkCryptoUtils.generateSignature(token, timeStamp, nonce, echoStr);

        if (!signature.equals(msgSignature)) {
            throw new RuntimeException("签名验证失败");
        }

        String result = decrypt(echoStr);
        return result;
    }
}
