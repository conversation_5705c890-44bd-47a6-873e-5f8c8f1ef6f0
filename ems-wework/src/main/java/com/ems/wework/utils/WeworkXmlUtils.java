package com.ems.wework.utils;

import org.dom4j.Document;
import org.dom4j.DocumentHelper;
import org.dom4j.Element;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 企业微信XML解析工具类
 * 
 * <AUTHOR>
 */
public class WeworkXmlUtils {
    
    private static final Logger log = LoggerFactory.getLogger(WeworkXmlUtils.class);
    
    /**
     * 解析XML字符串为Map
     * 
     * @param xmlString XML字符串
     * @return 解析后的Map
     */
    public static Map<String, String> parseXmlToMap(String xmlString) {
        Map<String, String> result = new HashMap<>();
        
        try {
            Document document = DocumentHelper.parseText(xmlString);
            Element root = document.getRootElement();
            
            @SuppressWarnings("unchecked")
            List<Element> elements = root.elements();
            
            for (Element element : elements) {
                result.put(element.getName(), element.getTextTrim());
            }
            
            log.debug("XML解析成功，解析出{}个字段", result.size());
            
        } catch (Exception e) {
            log.error("XML解析失败", e);
            throw new RuntimeException("XML解析失败", e);
        }
        
        return result;
    }
    
    /**
     * 将Map转换为XML字符串
     * 
     * @param data 数据Map
     * @param rootElementName 根元素名称
     * @return XML字符串
     */
    public static String mapToXml(Map<String, String> data, String rootElementName) {
        try {
            Document document = DocumentHelper.createDocument();
            Element root = document.addElement(rootElementName);
            
            for (Map.Entry<String, String> entry : data.entrySet()) {
                Element element = root.addElement(entry.getKey());
                if (needCDATA(entry.getValue())) {
                    element.addCDATA(entry.getValue());
                } else {
                    element.setText(entry.getValue());
                }
            }
            
            return document.asXML();
            
        } catch (Exception e) {
            log.error("Map转XML失败", e);
            throw new RuntimeException("Map转XML失败", e);
        }
    }
    
    /**
     * 创建简单的XML响应
     * 
     * @param msgType 消息类型
     * @param content 内容
     * @return XML字符串
     */
    public static String createSimpleResponse(String msgType, String content) {
        Map<String, String> data = new HashMap<>();
        data.put("MsgType", msgType);
        data.put("Content", content);
        data.put("CreateTime", String.valueOf(System.currentTimeMillis() / 1000));
        
        return mapToXml(data, "xml");
    }
    
    /**
     * 创建空响应（表示消息已处理，不需要回复）
     * 
     * @return 空字符串
     */
    public static String createEmptyResponse() {
        return "";
    }
    
    /**
     * 判断是否需要CDATA包装
     * 
     * @param value 值
     * @return 是否需要CDATA
     */
    private static boolean needCDATA(String value) {
        if (value == null) {
            return false;
        }
        
        // 包含特殊字符时使用CDATA
        return value.contains("<") || value.contains(">") || value.contains("&") || 
               value.contains("\"") || value.contains("'");
    }
    
    /**
     * 提取XML中的加密数据
     * 
     * @param xmlString XML字符串
     * @return 加密数据
     */
    public static String extractEncryptData(String xmlString) {
        try {
            Map<String, String> xmlMap = parseXmlToMap(xmlString);
            return xmlMap.get("Encrypt");
        } catch (Exception e) {
            log.error("提取加密数据失败", e);
            return null;
        }
    }
    
    /**
     * 验证XML格式
     *
     * @param xmlString XML字符串
     * @return 是否为有效XML
     */
    public static boolean isValidXml(String xmlString) {
        try {
            DocumentHelper.parseText(xmlString);
            return true;
        } catch (Exception e) {
            return false;
        }
    }

    /**
     * 生成加密XML响应
     *
     * @param encrypt 加密数据
     * @param signature 签名
     * @param timestamp 时间戳
     * @param nonce 随机数
     * @return 加密XML字符串
     */
    public static String generateEncryptXml(String encrypt, String signature, String timestamp, String nonce) {
        StringBuilder xml = new StringBuilder();
        xml.append("<xml>");
        xml.append("<Encrypt><![CDATA[").append(encrypt).append("]]></Encrypt>");
        xml.append("<MsgSignature><![CDATA[").append(signature).append("]]></MsgSignature>");
        xml.append("<TimeStamp>").append(timestamp).append("</TimeStamp>");
        xml.append("<Nonce><![CDATA[").append(nonce).append("]]></Nonce>");
        xml.append("</xml>");
        return xml.toString();
    }
}
