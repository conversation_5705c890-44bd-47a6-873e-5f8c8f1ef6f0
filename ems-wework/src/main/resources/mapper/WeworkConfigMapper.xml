<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
"http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.ems.wework.mapper.WeworkConfigMapper">
    
    <resultMap type="WeworkDomainConfig" id="WeworkConfigResult">
        <result property="configId"           column="config_id"           />
        <result property="corpId"             column="corp_id"             />
        <result property="corpSecret"         column="corp_secret"         />
        <result property="agentId"            column="agent_id"            />
        <result property="callbackToken"      column="callback_token"      />
        <result property="callbackAesKey"     column="callback_aes_key"    />
        <result property="oauth2RedirectUri"  column="oauth2_redirect_uri" />
        <result property="loginEnabled"       column="login_enabled"       />
        <result property="autoCreateUser"     column="auto_create_user"    />
        <result property="defaultRoleId"      column="default_role_id"     />
        <result property="defaultDeptId"      column="default_dept_id"     />
        <result property="status"             column="status"              />
        <result property="createBy"           column="create_by"           />
        <result property="createTime"         column="create_time"         />
        <result property="updateBy"           column="update_by"           />
        <result property="updateTime"         column="update_time"         />
        <result property="remark"             column="remark"              />
    </resultMap>

    <sql id="selectWeworkConfigVo">
        select config_id, corp_id, corp_secret, agent_id, callback_token, callback_aes_key, 
               oauth2_redirect_uri, login_enabled, auto_create_user, default_role_id, 
               default_dept_id, status, create_by, create_time, update_by, update_time, remark 
        from wework_config
    </sql>

    <select id="selectWeworkConfigList" parameterType="WeworkConfig" resultMap="WeworkConfigResult">
        <include refid="selectWeworkConfigVo"/>
        <where>  
            <if test="corpId != null  and corpId != ''"> and corp_id = #{corpId}</if>
            <if test="agentId != null  and agentId != ''"> and agent_id = #{agentId}</if>
            <if test="loginEnabled != null  and loginEnabled != ''"> and login_enabled = #{loginEnabled}</if>
            <if test="status != null  and status != ''"> and status = #{status}</if>
        </where>
    </select>
    
    <select id="selectWeworkConfigByConfigId" parameterType="Long" resultMap="WeworkConfigResult">
        <include refid="selectWeworkConfigVo"/>
        where config_id = #{configId}
    </select>

    <select id="selectWeworkConfigByCorpId" parameterType="String" resultMap="WeworkConfigResult">
        <include refid="selectWeworkConfigVo"/>
        where corp_id = #{corpId}
    </select>
        
    <insert id="insertWeworkConfig" parameterType="WeworkConfig" useGeneratedKeys="true" keyProperty="configId">
        insert into wework_config
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="corpId != null and corpId != ''">corp_id,</if>
            <if test="corpSecret != null and corpSecret != ''">corp_secret,</if>
            <if test="agentId != null and agentId != ''">agent_id,</if>
            <if test="callbackToken != null">callback_token,</if>
            <if test="callbackAesKey != null">callback_aes_key,</if>
            <if test="oauth2RedirectUri != null">oauth2_redirect_uri,</if>
            <if test="loginEnabled != null">login_enabled,</if>
            <if test="autoCreateUser != null">auto_create_user,</if>
            <if test="defaultRoleId != null">default_role_id,</if>
            <if test="defaultDeptId != null">default_dept_id,</if>
            <if test="status != null">status,</if>
            <if test="createBy != null">create_by,</if>
            <if test="remark != null">remark,</if>
            create_time
         </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="corpId != null and corpId != ''">#{corpId},</if>
            <if test="corpSecret != null and corpSecret != ''">#{corpSecret},</if>
            <if test="agentId != null and agentId != ''">#{agentId},</if>
            <if test="callbackToken != null">#{callbackToken},</if>
            <if test="callbackAesKey != null">#{callbackAesKey},</if>
            <if test="oauth2RedirectUri != null">#{oauth2RedirectUri},</if>
            <if test="loginEnabled != null">#{loginEnabled},</if>
            <if test="autoCreateUser != null">#{autoCreateUser},</if>
            <if test="defaultRoleId != null">#{defaultRoleId},</if>
            <if test="defaultDeptId != null">#{defaultDeptId},</if>
            <if test="status != null">#{status},</if>
            <if test="createBy != null">#{createBy},</if>
            <if test="remark != null">#{remark},</if>
            sysdate()
         </trim>
    </insert>

    <update id="updateWeworkConfig" parameterType="WeworkConfig">
        update wework_config
        <trim prefix="SET" suffixOverrides=",">
            <if test="corpId != null and corpId != ''">corp_id = #{corpId},</if>
            <if test="corpSecret != null and corpSecret != ''">corp_secret = #{corpSecret},</if>
            <if test="agentId != null and agentId != ''">agent_id = #{agentId},</if>
            <if test="callbackToken != null">callback_token = #{callbackToken},</if>
            <if test="callbackAesKey != null">callback_aes_key = #{callbackAesKey},</if>
            <if test="oauth2RedirectUri != null">oauth2_redirect_uri = #{oauth2RedirectUri},</if>
            <if test="loginEnabled != null">login_enabled = #{loginEnabled},</if>
            <if test="autoCreateUser != null">auto_create_user = #{autoCreateUser},</if>
            <if test="defaultRoleId != null">default_role_id = #{defaultRoleId},</if>
            <if test="defaultDeptId != null">default_dept_id = #{defaultDeptId},</if>
            <if test="status != null">status = #{status},</if>
            <if test="updateBy != null">update_by = #{updateBy},</if>
            <if test="remark != null">remark = #{remark},</if>
            update_time = sysdate()
        </trim>
        where config_id = #{configId}
    </update>

    <delete id="deleteWeworkConfigByConfigId" parameterType="Long">
        delete from wework_config where config_id = #{configId}
    </delete>

    <delete id="deleteWeworkConfigByConfigIds" parameterType="String">
        delete from wework_config where config_id in 
        <foreach item="configId" collection="array" open="(" separator="," close=")">
            #{configId}
        </foreach>
    </delete>

</mapper>
