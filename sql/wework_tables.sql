-- 企业微信第三方应用数据库表结构
-- 作者: ems
-- 日期: 2025-08-07

-- 1. 扩展用户表，添加企业微信相关字段
ALTER TABLE sys_user ADD COLUMN wework_userid VARCHAR(64) COMMENT '企业微信用户ID';
ALTER TABLE sys_user ADD COLUMN wework_mobile VARCHAR(20) COMMENT '企业微信手机号';
ALTER TABLE sys_user ADD COLUMN wework_avatar VARCHAR(500) COMMENT '企业微信头像URL';
ALTER TABLE sys_user ADD COLUMN wework_name VARCHAR(50) COMMENT '企业微信姓名';
ALTER TABLE sys_user ADD COLUMN wework_department VARCHAR(200) COMMENT '企业微信部门';
ALTER TABLE sys_user ADD COLUMN wework_bind_time DATETIME COMMENT '企业微信绑定时间';

-- 添加索引提升查询性能
CREATE INDEX idx_sys_user_wework_userid ON sys_user(wework_userid);

-- 2. 创建企业微信配置表
CREATE TABLE wework_config (
    config_id BIGINT(20) NOT NULL AUTO_INCREMENT COMMENT '配置ID',
    corp_id VARCHAR(64) NOT NULL COMMENT '企业ID',
    corp_secret VARCHAR(128) NOT NULL COMMENT '应用密钥',
    agent_id VARCHAR(32) NOT NULL COMMENT '应用ID',
    callback_token VARCHAR(64) COMMENT '回调Token',
    callback_aes_key VARCHAR(128) COMMENT '回调加密密钥',
    oauth2_redirect_uri VARCHAR(500) COMMENT 'OAuth2回调地址',
    login_enabled CHAR(1) DEFAULT '1' COMMENT '登录开关：0-关闭，1-开启',
    auto_create_user CHAR(1) DEFAULT '1' COMMENT '自动创建用户：0-否，1-是',
    default_role_id BIGINT(20) COMMENT '默认角色ID',
    default_dept_id BIGINT(20) COMMENT '默认部门ID',
    status CHAR(1) DEFAULT '1' COMMENT '配置状态：0-停用，1-正常',
    create_by VARCHAR(64) DEFAULT '' COMMENT '创建者',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    update_by VARCHAR(64) DEFAULT '' COMMENT '更新者',
    update_time DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    remark VARCHAR(500) DEFAULT NULL COMMENT '备注',
    PRIMARY KEY (config_id),
    UNIQUE KEY uk_corp_id (corp_id)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='企业微信配置表';

-- 3. 创建企业微信回调日志表
CREATE TABLE wework_callback_log (
    log_id BIGINT(20) NOT NULL AUTO_INCREMENT COMMENT '日志ID',
    msg_type VARCHAR(32) COMMENT '消息类型：event、text等',
    event_type VARCHAR(32) COMMENT '事件类型：subscribe、unsubscribe等',
    info_type VARCHAR(32) COMMENT '数据回调InfoType',
    from_user VARCHAR(64) COMMENT '发送方用户ID',
    to_user VARCHAR(64) COMMENT '接收方用户ID',
    agent_id VARCHAR(32) COMMENT '应用ID',
    msg_content TEXT COMMENT '消息内容（解密后）',
    response_content TEXT COMMENT '响应内容',
    process_status CHAR(1) DEFAULT '1' COMMENT '处理状态：0-失败，1-成功',
    error_msg VARCHAR(500) COMMENT '错误信息',
    process_time BIGINT(20) COMMENT '处理耗时(毫秒)',
    request_ip VARCHAR(50) COMMENT '请求IP',
    user_agent VARCHAR(500) COMMENT '用户代理',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    PRIMARY KEY (log_id),
    INDEX idx_msg_type (msg_type),
    INDEX idx_event_type (event_type),
    INDEX idx_info_type (info_type),
    INDEX idx_from_user (from_user),
    INDEX idx_create_time (create_time),
    INDEX idx_process_status (process_status)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='企业微信回调日志表';

-- 4. 创建企业微信用户同步记录表
CREATE TABLE wework_user_sync (
    sync_id BIGINT(20) NOT NULL AUTO_INCREMENT COMMENT '同步ID',
    wework_userid VARCHAR(64) NOT NULL COMMENT '企业微信用户ID',
    sys_user_id BIGINT(20) COMMENT '系统用户ID',
    sync_type CHAR(1) NOT NULL COMMENT '同步类型：1-创建，2-更新，3-删除',
    sync_status CHAR(1) DEFAULT '1' COMMENT '同步状态：0-失败，1-成功',
    sync_data TEXT COMMENT '同步数据（JSON格式）',
    error_msg VARCHAR(500) COMMENT '错误信息',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    PRIMARY KEY (sync_id),
    INDEX idx_wework_userid (wework_userid),
    INDEX idx_sys_user_id (sys_user_id),
    INDEX idx_sync_type (sync_type),
    INDEX idx_sync_status (sync_status),
    INDEX idx_create_time (create_time)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='企业微信用户同步记录表';

-- 5. 创建企业微信访问令牌缓存表（可选，也可以使用Redis）
CREATE TABLE wework_access_token (
    token_id BIGINT(20) NOT NULL AUTO_INCREMENT COMMENT '令牌ID',
    corp_id VARCHAR(64) NOT NULL COMMENT '企业ID',
    agent_id VARCHAR(32) NOT NULL COMMENT '应用ID',
    access_token TEXT NOT NULL COMMENT '访问令牌',
    expires_in INT(11) NOT NULL COMMENT '有效期（秒）',
    create_time DATETIME DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    expire_time DATETIME NOT NULL COMMENT '过期时间',
    PRIMARY KEY (token_id),
    UNIQUE KEY uk_corp_agent (corp_id, agent_id),
    INDEX idx_expire_time (expire_time)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COMMENT='企业微信访问令牌缓存表';

-- 6. 插入默认配置数据
INSERT INTO wework_config (
    corp_id, 
    corp_secret, 
    agent_id, 
    callback_token, 
    callback_aes_key,
    oauth2_redirect_uri,
    login_enabled,
    auto_create_user,
    default_role_id,
    default_dept_id,
    status,
    create_by,
    remark
) VALUES (
    'your_corp_id',
    'your_corp_secret', 
    'your_agent_id',
    'your_callback_token',
    'your_callback_aes_key',
    'http://your-domain.com/wework/auth/callback',
    '1',
    '1',
    2,
    100,
    '1',
    'admin',
    '企业微信默认配置，请根据实际情况修改'
);

-- 7. 创建视图：企业微信用户信息视图
CREATE VIEW v_wework_user_info AS
SELECT 
    u.user_id,
    u.user_name,
    u.nick_name,
    u.email,
    u.phonenumber,
    u.wework_userid,
    u.wework_mobile,
    u.wework_avatar,
    u.wework_name,
    u.wework_department,
    u.wework_bind_time,
    u.status,
    u.create_time,
    d.dept_name,
    r.role_name
FROM sys_user u
LEFT JOIN sys_dept d ON u.dept_id = d.dept_id
LEFT JOIN sys_user_role ur ON u.user_id = ur.user_id
LEFT JOIN sys_role r ON ur.role_id = r.role_id
WHERE u.wework_userid IS NOT NULL AND u.wework_userid != '';

-- 8. 创建存储过程：清理过期的访问令牌
DELIMITER $$
CREATE PROCEDURE sp_clean_expired_tokens()
BEGIN
    DELETE FROM wework_access_token 
    WHERE expire_time < NOW();
    
    SELECT ROW_COUNT() as deleted_count;
END$$
DELIMITER ;

-- 9. 创建存储过程：获取有效的访问令牌
DELIMITER $$
CREATE PROCEDURE sp_get_valid_token(
    IN p_corp_id VARCHAR(64),
    IN p_agent_id VARCHAR(32)
)
BEGIN
    SELECT access_token, expires_in, expire_time
    FROM wework_access_token
    WHERE corp_id = p_corp_id 
      AND agent_id = p_agent_id
      AND expire_time > NOW()
    ORDER BY create_time DESC
    LIMIT 1;
END$$
DELIMITER ;

-- 10. 创建触发器：用户绑定企业微信时自动更新绑定时间
DELIMITER $$
CREATE TRIGGER tr_user_wework_bind_time
    BEFORE UPDATE ON sys_user
    FOR EACH ROW
BEGIN
    IF OLD.wework_userid IS NULL AND NEW.wework_userid IS NOT NULL THEN
        SET NEW.wework_bind_time = NOW();
    END IF;
END$$
DELIMITER ;
