# 企业微信第三方应用使用示例

## 概述

本文档提供企业微信第三方应用模块的详细使用示例，包括前端集成、后端调用、回调处理等场景。

## 前端集成示例

### 1. Vue.js集成示例

#### 1.1 企业微信登录组件

```vue
<template>
  <div class="wework-login">
    <el-button 
      type="primary" 
      @click="handleWeworkLogin"
      :loading="loading"
      icon="el-icon-user">
      企业微信登录
    </el-button>
  </div>
</template>

<script>
import { getWeworkAuthUrl, weworkLogin } from '@/api/wework'

export default {
  name: 'WeworkLogin',
  data() {
    return {
      loading: false
    }
  },
  mounted() {
    // 检查URL中是否有企业微信回调的code参数
    this.handleWeworkCallback()
  },
  methods: {
    // 处理企业微信登录
    async handleWeworkLogin() {
      try {
        this.loading = true
        
        // 获取当前页面URL作为回调地址
        const redirectUri = window.location.origin + window.location.pathname
        
        // 获取企业微信授权URL
        const response = await getWeworkAuthUrl(redirectUri)
        
        if (response.code === 200) {
          // 跳转到企业微信授权页面
          window.location.href = response.data.authUrl
        } else {
          this.$message.error(response.msg || '获取授权URL失败')
        }
      } catch (error) {
        console.error('企业微信登录失败:', error)
        this.$message.error('企业微信登录失败')
      } finally {
        this.loading = false
      }
    },
    
    // 处理企业微信回调
    async handleWeworkCallback() {
      const urlParams = new URLSearchParams(window.location.search)
      const code = urlParams.get('code')
      const state = urlParams.get('state')
      
      if (code) {
        try {
          this.loading = true
          
          // 使用code进行登录
          const response = await weworkLogin({ code, state })
          
          if (response.code === 200) {
            // 保存token
            this.$store.dispatch('user/setToken', response.data.token)
            
            // 获取用户信息
            await this.$store.dispatch('user/getInfo')
            
            this.$message.success('登录成功')
            
            // 跳转到首页
            this.$router.push('/')
          } else {
            this.$message.error(response.msg || '登录失败')
          }
        } catch (error) {
          console.error('企业微信登录回调处理失败:', error)
          this.$message.error('登录失败')
        } finally {
          this.loading = false
          
          // 清理URL中的参数
          window.history.replaceState({}, document.title, window.location.pathname)
        }
      }
    }
  }
}
</script>
```

#### 1.2 API调用封装

```javascript
// api/wework.js
import request from '@/utils/request'

// 获取企业微信授权URL
export function getWeworkAuthUrl(redirectUri, state) {
  return request({
    url: '/wework/auth/url',
    method: 'get',
    params: {
      redirectUri,
      state
    }
  })
}

// 企业微信登录
export function weworkLogin(data) {
  return request({
    url: '/wework/auth/login',
    method: 'post',
    data
  })
}

// 获取企业微信登录状态
export function getWeworkStatus() {
  return request({
    url: '/wework/auth/status',
    method: 'get'
  })
}

// 绑定企业微信用户
export function bindWeworkUser(code) {
  return request({
    url: '/wework/auth/bind',
    method: 'post',
    params: { code }
  })
}

// 解绑企业微信用户
export function unbindWeworkUser() {
  return request({
    url: '/wework/auth/unbind',
    method: 'post'
  })
}
```

### 2. React集成示例

#### 2.1 企业微信登录Hook

```javascript
// hooks/useWeworkAuth.js
import { useState, useEffect } from 'react'
import { message } from 'antd'
import { getWeworkAuthUrl, weworkLogin } from '../api/wework'

export const useWeworkAuth = () => {
  const [loading, setLoading] = useState(false)

  // 处理企业微信登录
  const handleWeworkLogin = async () => {
    try {
      setLoading(true)
      
      const redirectUri = window.location.origin + window.location.pathname
      const response = await getWeworkAuthUrl(redirectUri)
      
      if (response.code === 200) {
        window.location.href = response.data.authUrl
      } else {
        message.error(response.msg || '获取授权URL失败')
      }
    } catch (error) {
      console.error('企业微信登录失败:', error)
      message.error('企业微信登录失败')
    } finally {
      setLoading(false)
    }
  }

  // 处理企业微信回调
  const handleWeworkCallback = async () => {
    const urlParams = new URLSearchParams(window.location.search)
    const code = urlParams.get('code')
    const state = urlParams.get('state')

    if (code) {
      try {
        setLoading(true)
        
        const response = await weworkLogin({ code, state })
        
        if (response.code === 200) {
          // 保存token到localStorage
          localStorage.setItem('token', response.data.token)
          
          message.success('登录成功')
          
          // 刷新页面或跳转
          window.location.href = '/'
        } else {
          message.error(response.msg || '登录失败')
        }
      } catch (error) {
        console.error('企业微信登录回调处理失败:', error)
        message.error('登录失败')
      } finally {
        setLoading(false)
        
        // 清理URL参数
        window.history.replaceState({}, document.title, window.location.pathname)
      }
    }
  }

  useEffect(() => {
    handleWeworkCallback()
  }, [])

  return {
    loading,
    handleWeworkLogin
  }
}
```

#### 2.2 企业微信登录组件

```javascript
// components/WeworkLogin.jsx
import React from 'react'
import { Button } from 'antd'
import { UserOutlined } from '@ant-design/icons'
import { useWeworkAuth } from '../hooks/useWeworkAuth'

const WeworkLogin = () => {
  const { loading, handleWeworkLogin } = useWeworkAuth()

  return (
    <Button
      type="primary"
      icon={<UserOutlined />}
      loading={loading}
      onClick={handleWeworkLogin}
    >
      企业微信登录
    </Button>
  )
}

export default WeworkLogin
```

## 后端集成示例

### 1. Spring Boot集成

#### 1.1 自定义认证过滤器

```java
@Component
public class WeworkAuthenticationFilter extends OncePerRequestFilter {
    
    @Autowired
    private IWeworkAuthService authService;
    
    @Autowired
    private TokenService tokenService;
    
    @Override
    protected void doFilterInternal(HttpServletRequest request, 
                                  HttpServletResponse response, 
                                  FilterChain filterChain) throws ServletException, IOException {
        
        // 检查是否是企业微信登录请求
        if (isWeworkLoginRequest(request)) {
            try {
                String code = request.getParameter("code");
                if (StringUtils.isNotEmpty(code)) {
                    // 执行企业微信登录
                    String token = authService.login(code);
                    
                    // 设置认证信息
                    LoginUser loginUser = tokenService.getLoginUser(token);
                    UsernamePasswordAuthenticationToken authToken = 
                        new UsernamePasswordAuthenticationToken(loginUser, null, loginUser.getAuthorities());
                    SecurityContextHolder.getContext().setAuthentication(authToken);
                }
            } catch (Exception e) {
                logger.error("企业微信自动登录失败", e);
            }
        }
        
        filterChain.doFilter(request, response);
    }
    
    private boolean isWeworkLoginRequest(HttpServletRequest request) {
        String code = request.getParameter("code");
        String state = request.getParameter("state");
        return StringUtils.isNotEmpty(code) && "wework".equals(state);
    }
}
```

#### 1.2 企业微信用户同步服务

```java
@Service
public class WeworkUserSyncService {
    
    @Autowired
    private IWeworkApiService apiService;
    
    @Autowired
    private ISysUserService userService;
    
    @Autowired
    private IWeworkAuthService authService;
    
    /**
     * 同步企业微信部门用户
     */
    @Async
    public void syncDepartmentUsers(String deptId) {
        try {
            // 获取access_token
            String accessToken = apiService.getAccessToken(corpId, corpSecret);
            
            // 获取部门用户列表
            String userListJson = apiService.getDepartmentUsers(accessToken, deptId, true);
            JSONObject result = JSON.parseObject(userListJson);
            
            if (result.getIntValue("errcode") == 0) {
                JSONArray userList = result.getJSONArray("userlist");
                
                for (int i = 0; i < userList.size(); i++) {
                    JSONObject userJson = userList.getJSONObject(i);
                    WeworkUser weworkUser = JSON.parseObject(userJson.toJSONString(), WeworkUser.class);
                    
                    // 同步用户信息
                    syncSingleUser(weworkUser);
                }
            }
        } catch (Exception e) {
            logger.error("同步企业微信部门用户失败", e);
        }
    }
    
    /**
     * 同步单个用户
     */
    private void syncSingleUser(WeworkUser weworkUser) {
        try {
            SysUser existUser = authService.getUserByWeworkId(weworkUser.getUserId());
            
            if (existUser != null) {
                // 用户已存在，更新信息
                authService.syncWeworkUser(weworkUser);
            } else {
                // 用户不存在，创建新用户
                createUserFromWework(weworkUser);
            }
        } catch (Exception e) {
            logger.error("同步用户失败: {}", weworkUser.getUserId(), e);
        }
    }
    
    /**
     * 从企业微信用户创建本地用户
     */
    private void createUserFromWework(WeworkUser weworkUser) {
        SysUser sysUser = new SysUser();
        sysUser.setUserName(weworkUser.getUserId());
        sysUser.setNickName(weworkUser.getName());
        sysUser.setEmail(weworkUser.getEmail());
        sysUser.setPhonenumber(weworkUser.getMobile());
        sysUser.setWeworkUserid(weworkUser.getUserId());
        sysUser.setWeworkMobile(weworkUser.getMobile());
        sysUser.setWeworkAvatar(weworkUser.getAvatar());
        sysUser.setWeworkName(weworkUser.getName());
        sysUser.setWeworkDepartment(weworkUser.getDepartment().toString());
        sysUser.setWeworkBindTime(new Date());
        sysUser.setStatus("1");
        sysUser.setCreateBy("system");
        
        // 设置默认部门和角色
        sysUser.setDeptId(100L); // 默认部门
        
        userService.insertUser(sysUser);
        
        // 分配默认角色
        userService.insertUserAuth(sysUser.getUserId(), new Long[]{2L}); // 普通用户角色
    }
}
```

### 2. 消息推送示例

#### 2.1 消息推送服务

```java
@Service
public class WeworkMessageService {
    
    @Autowired
    private IWeworkApiService apiService;
    
    @Autowired
    private WeworkConfig weworkConfig;
    
    /**
     * 发送文本消息
     */
    public boolean sendTextMessage(String toUser, String content) {
        try {
            String accessToken = apiService.getAccessToken(
                weworkConfig.getCorpId(), 
                weworkConfig.getCorpSecret()
            );
            
            WeworkMessage message = new WeworkMessage();
            message.setToUser(toUser);
            message.setMsgType("text");
            message.setAgentId(weworkConfig.getAgentId());
            message.setText(new WeworkMessage.TextContent(content));
            
            String result = apiService.sendMessage(accessToken, message);
            JSONObject resultJson = JSON.parseObject(result);
            
            return resultJson.getIntValue("errcode") == 0;
        } catch (Exception e) {
            logger.error("发送企业微信消息失败", e);
            return false;
        }
    }
    
    /**
     * 发送卡片消息
     */
    public boolean sendCardMessage(String toUser, String title, String description, String url) {
        try {
            String accessToken = apiService.getAccessToken(
                weworkConfig.getCorpId(), 
                weworkConfig.getCorpSecret()
            );
            
            WeworkMessage message = new WeworkMessage();
            message.setToUser(toUser);
            message.setMsgType("textcard");
            message.setAgentId(weworkConfig.getAgentId());
            message.setTextCard(new WeworkMessage.TextCardContent(title, description, url, "详情"));
            
            String result = apiService.sendMessage(accessToken, message);
            JSONObject resultJson = JSON.parseObject(result);
            
            return resultJson.getIntValue("errcode") == 0;
        } catch (Exception e) {
            logger.error("发送企业微信卡片消息失败", e);
            return false;
        }
    }
}
```

#### 2.2 业务集成示例

```java
@Service
public class NotificationService {
    
    @Autowired
    private WeworkMessageService weworkMessageService;
    
    /**
     * 发送工作流审批通知
     */
    public void sendWorkflowNotification(String userId, String workflowTitle, String workflowUrl) {
        try {
            // 根据用户ID获取企业微信用户ID
            SysUser user = userService.selectUserById(Long.valueOf(userId));
            if (user != null && StringUtils.isNotEmpty(user.getWeworkUserid())) {
                String title = "待办审批";
                String description = String.format("您有新的待办事项：%s，请及时处理。", workflowTitle);
                
                weworkMessageService.sendCardMessage(
                    user.getWeworkUserid(), 
                    title, 
                    description, 
                    workflowUrl
                );
            }
        } catch (Exception e) {
            logger.error("发送工作流通知失败", e);
        }
    }
    
    /**
     * 发送系统公告
     */
    public void sendSystemAnnouncement(String content) {
        try {
            // 获取所有绑定企业微信的用户
            List<SysUser> users = userService.selectUserListWithWework();
            
            for (SysUser user : users) {
                weworkMessageService.sendTextMessage(user.getWeworkUserid(), content);
            }
        } catch (Exception e) {
            logger.error("发送系统公告失败", e);
        }
    }
}
```

## 回调处理示例

### 1. 自定义回调处理器

```java
@Component
public class CustomWeworkCallbackHandler {
    
    @Autowired
    private WeworkUserSyncService userSyncService;
    
    @Autowired
    private NotificationService notificationService;
    
    /**
     * 处理用户入职事件
     */
    @EventListener
    public void handleUserJoinEvent(WeworkUserJoinEvent event) {
        try {
            WeworkUser weworkUser = event.getWeworkUser();
            
            // 自动创建本地用户
            userSyncService.syncSingleUser(weworkUser);
            
            // 发送欢迎消息
            String welcomeMessage = String.format("欢迎 %s 加入公司！", weworkUser.getName());
            notificationService.sendWelcomeMessage(weworkUser.getUserId(), welcomeMessage);
            
        } catch (Exception e) {
            logger.error("处理用户入职事件失败", e);
        }
    }
    
    /**
     * 处理用户离职事件
     */
    @EventListener
    public void handleUserLeaveEvent(WeworkUserLeaveEvent event) {
        try {
            String weworkUserId = event.getWeworkUserId();
            
            // 禁用本地用户
            SysUser user = authService.getUserByWeworkId(weworkUserId);
            if (user != null) {
                user.setStatus("1"); // 停用
                userService.updateUser(user);
            }
            
        } catch (Exception e) {
            logger.error("处理用户离职事件失败", e);
        }
    }
}
```

### 2. 事件发布

```java
@Service
public class WeworkEventPublisher {
    
    @Autowired
    private ApplicationEventPublisher eventPublisher;
    
    /**
     * 发布用户变更事件
     */
    public void publishUserChangeEvent(String changeType, WeworkUser weworkUser) {
        switch (changeType) {
            case "create_user":
                eventPublisher.publishEvent(new WeworkUserJoinEvent(weworkUser));
                break;
            case "delete_user":
                eventPublisher.publishEvent(new WeworkUserLeaveEvent(weworkUser.getUserId()));
                break;
            case "update_user":
                eventPublisher.publishEvent(new WeworkUserUpdateEvent(weworkUser));
                break;
        }
    }
}
```

## 测试示例

### 1. 单元测试

```java
@SpringBootTest
@TestPropertySource(properties = {
    "wework.corp-id=test_corp_id",
    "wework.corp-secret=test_corp_secret",
    "wework.agent-id=test_agent_id"
})
public class WeworkIntegrationTest {
    
    @Autowired
    private IWeworkAuthService authService;
    
    @MockBean
    private IWeworkApiService apiService;
    
    @Test
    public void testWeworkLogin() {
        // 模拟API调用
        when(apiService.getAccessToken(anyString(), anyString()))
            .thenReturn("mock_access_token");
        
        WeworkUser mockUser = new WeworkUser();
        mockUser.setUserId("test_user");
        mockUser.setName("测试用户");
        mockUser.setStatus(1);
        mockUser.setEnable(1);
        
        when(apiService.getUserInfoByCode(anyString(), anyString()))
            .thenReturn(mockUser);
        
        // 执行登录测试
        String token = authService.login("test_code");
        
        assertNotNull(token);
    }
}
```

### 2. 集成测试

```java
@SpringBootTest(webEnvironment = SpringBootTest.WebEnvironment.RANDOM_PORT)
@AutoConfigureTestDatabase(replace = AutoConfigureTestDatabase.Replace.NONE)
public class WeworkControllerIntegrationTest {
    
    @Autowired
    private TestRestTemplate restTemplate;
    
    @Test
    public void testGetAuthUrl() {
        String url = "/wework/auth/url?redirectUri=http://test.com/callback";
        
        ResponseEntity<Map> response = restTemplate.getForEntity(url, Map.class);
        
        assertEquals(HttpStatus.OK, response.getStatusCode());
        
        Map<String, Object> body = response.getBody();
        assertEquals(200, body.get("code"));
        assertNotNull(body.get("data"));
    }
}
```

## 最佳实践

### 1. 错误处理

```java
@ControllerAdvice
public class WeworkExceptionHandler {
    
    @ExceptionHandler(WeworkAuthException.class)
    public AjaxResult handleWeworkAuthException(WeworkAuthException e) {
        logger.error("企业微信认证异常", e);
        return AjaxResult.error("企业微信认证失败：" + e.getMessage());
    }
    
    @ExceptionHandler(WeworkApiException.class)
    public AjaxResult handleWeworkApiException(WeworkApiException e) {
        logger.error("企业微信API调用异常", e);
        return AjaxResult.error("企业微信服务异常，请稍后重试");
    }
}
```

### 2. 缓存优化

```java
@Service
public class WeworkCacheService {
    
    @Autowired
    private RedisTemplate<String, Object> redisTemplate;
    
    @Cacheable(value = "wework:user", key = "#userId", unless = "#result == null")
    public WeworkUser getUserInfo(String userId) {
        // 从企业微信API获取用户信息
        return apiService.getUserDetail(accessToken, userId);
    }
    
    @CacheEvict(value = "wework:user", key = "#userId")
    public void evictUserCache(String userId) {
        // 清除用户缓存
    }
}
```

### 3. 监控和日志

```java
@Aspect
@Component
public class WeworkMonitorAspect {
    
    @Around("execution(* com.ems.wework.service.*.*(..))")
    public Object monitor(ProceedingJoinPoint joinPoint) throws Throwable {
        long startTime = System.currentTimeMillis();
        String methodName = joinPoint.getSignature().getName();
        
        try {
            Object result = joinPoint.proceed();
            
            long endTime = System.currentTimeMillis();
            logger.info("企业微信服务调用成功: {}, 耗时: {}ms", methodName, endTime - startTime);
            
            return result;
        } catch (Exception e) {
            long endTime = System.currentTimeMillis();
            logger.error("企业微信服务调用失败: {}, 耗时: {}ms", methodName, endTime - startTime, e);
            throw e;
        }
    }
}
```

这些示例展示了企业微信第三方应用模块在不同场景下的使用方法，可以根据实际需求进行调整和扩展。
