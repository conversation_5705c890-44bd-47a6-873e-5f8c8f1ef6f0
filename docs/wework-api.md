# 企业微信第三方应用API文档

## 概述

本文档描述了企业微信第三方应用模块的API接口，包括认证登录、回调处理、配置管理等功能。

## 基础信息

- **模块名称**: ems-wework
- **基础路径**: `/wework`
- **版本**: 1.0.0
- **认证方式**: JWT Token（部分接口支持匿名访问）

## 认证相关接口

### 1. 获取企业微信授权URL

**接口地址**: `GET /wework/auth/url`

**请求参数**:

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| redirectUri | String | 是 | 授权回调地址 |
| state | String | 否 | 状态参数，用于防止CSRF攻击 |

**响应示例**:
```json
{
  "code": 200,
  "msg": "获取授权URL成功",
  "data": {
    "authUrl": "https://open.weixin.qq.com/connect/oauth2/authorize?appid=corp_id&redirect_uri=callback_url&response_type=code&scope=snsapi_base&agentid=agent_id&state=state#wechat_redirect"
  }
}
```

### 2. 企业微信授权登录

**接口地址**: `POST /wework/auth/login`

**请求体**:
```json
{
  "code": "授权码",
  "state": "状态参数",
  "corpId": "企业ID（可选）",
  "agentId": "应用ID（可选）"
}
```

**响应示例**:
```json
{
  "code": 200,
  "msg": "登录成功",
  "data": {
    "token": "eyJhbGciOiJIUzUxMiJ9..."
  }
}
```

### 3. 获取企业微信登录状态

**接口地址**: `GET /wework/auth/status`

**响应示例**:
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": {
    "enabled": true,
    "message": "企业微信登录已启用"
  }
}
```

### 4. 绑定企业微信用户

**接口地址**: `POST /wework/auth/bind`

**请求参数**:

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| code | String | 是 | 企业微信授权码 |

**响应示例**:
```json
{
  "code": 200,
  "msg": "绑定成功"
}
```

### 5. 解绑企业微信用户

**接口地址**: `POST /wework/auth/unbind`

**响应示例**:
```json
{
  "code": 200,
  "msg": "解绑成功"
}
```

## 回调接口

### 1. 数据回调接口

**接口地址**: 
- `GET /wework/callback/data` - URL验证
- `POST /wework/callback/data` - 事件推送

**URL验证参数**:

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| msg_signature | String | 是 | 签名 |
| timestamp | String | 是 | 时间戳 |
| nonce | String | 是 | 随机数 |
| echostr | String | 是 | 随机字符串 |

**事件推送参数**:

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| msg_signature | String | 是 | 签名 |
| timestamp | String | 是 | 时间戳 |
| nonce | String | 是 | 随机数 |

**请求体**: 加密的XML数据

**支持的InfoType**:
- `suite_ticket`: 推送suite_ticket
- `create_auth`: 授权成功
- `change_auth`: 授权变更
- `cancel_auth`: 授权取消
- `change_contact`: 通讯录变更

### 2. 指令回调接口

**接口地址**: 
- `GET /wework/callback/command` - URL验证
- `POST /wework/callback/command` - 事件推送

**参数格式**: 与数据回调接口相同

**支持的消息类型**:
- `event`: 事件消息（订阅、取消订阅、菜单点击等）
- `text`: 文本消息
- `image`: 图片消息
- `voice`: 语音消息
- `video`: 视频消息
- `file`: 文件消息
- `location`: 位置消息

## 配置管理接口

### 1. 查询企业微信配置列表

**接口地址**: `GET /wework/config/list`

**权限要求**: `wework:config:list`

**请求参数**:

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| corpId | String | 否 | 企业ID |
| status | String | 否 | 状态（0-停用，1-正常） |
| pageNum | Integer | 否 | 页码 |
| pageSize | Integer | 否 | 每页数量 |

### 2. 获取企业微信配置详情

**接口地址**: `GET /wework/config/{configId}`

**权限要求**: `wework:config:query`

### 3. 新增企业微信配置

**接口地址**: `POST /wework/config`

**权限要求**: `wework:config:add`

**请求体**:
```json
{
  "corpId": "企业ID",
  "corpSecret": "应用密钥",
  "agentId": "应用ID",
  "callbackToken": "回调Token",
  "callbackAesKey": "回调加密密钥",
  "oauth2RedirectUri": "OAuth2回调地址",
  "loginEnabled": "1",
  "autoCreateUser": "1",
  "defaultRoleId": 2,
  "defaultDeptId": 100,
  "status": "1",
  "remark": "备注信息"
}
```

### 4. 修改企业微信配置

**接口地址**: `PUT /wework/config`

**权限要求**: `wework:config:edit`

### 5. 删除企业微信配置

**接口地址**: `DELETE /wework/config/{configIds}`

**权限要求**: `wework:config:remove`

### 6. 测试企业微信连接

**接口地址**: `POST /wework/config/test`

**权限要求**: `wework:config:test`

**请求体**: 企业微信配置信息

**响应示例**:
```json
{
  "code": 200,
  "msg": "连接测试成功"
}
```

### 7. 获取当前生效配置

**接口地址**: `GET /wework/config/current`

**权限要求**: `wework:config:query`

**响应示例**:
```json
{
  "code": 200,
  "msg": "操作成功",
  "data": {
    "configId": 1,
    "corpId": "corp_id",
    "corpSecret": "******",
    "agentId": "agent_id",
    "loginEnabled": "1",
    "status": "1"
  }
}
```

## 错误码说明

| 错误码 | 说明 |
|--------|------|
| 200 | 操作成功 |
| 400 | 请求参数错误 |
| 401 | 未授权访问 |
| 403 | 权限不足 |
| 500 | 服务器内部错误 |

## 企业微信相关错误码

| 错误码 | 说明 |
|--------|------|
| 40001 | 不合法的secret参数 |
| 40003 | 不合法的UserID |
| 40013 | 不合法的corpid |
| 40014 | 不合法的access_token |
| 42001 | access_token超时 |
| 42007 | 用户修改了授权关系 |

## 使用示例

### JavaScript示例

```javascript
// 获取企业微信授权URL
async function getWeworkAuthUrl() {
  const response = await fetch('/wework/auth/url?redirectUri=' + encodeURIComponent(window.location.origin + '/callback'));
  const result = await response.json();
  if (result.code === 200) {
    window.location.href = result.data.authUrl;
  }
}

// 企业微信登录
async function weworkLogin(code) {
  const response = await fetch('/wework/auth/login', {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json'
    },
    body: JSON.stringify({ code: code })
  });
  const result = await response.json();
  if (result.code === 200) {
    localStorage.setItem('token', result.data.token);
    // 登录成功，跳转到主页
    window.location.href = '/';
  }
}
```

### Java示例

```java
// 使用RestTemplate调用企业微信登录接口
@Autowired
private RestTemplate restTemplate;

public String weworkLogin(String code) {
    String url = "http://localhost:8080/wework/auth/login";
    
    Map<String, String> requestBody = new HashMap<>();
    requestBody.put("code", code);
    
    HttpHeaders headers = new HttpHeaders();
    headers.setContentType(MediaType.APPLICATION_JSON);
    
    HttpEntity<Map<String, String>> entity = new HttpEntity<>(requestBody, headers);
    
    ResponseEntity<Map> response = restTemplate.postForEntity(url, entity, Map.class);
    
    if (response.getStatusCode() == HttpStatus.OK) {
        Map<String, Object> result = response.getBody();
        if ((Integer) result.get("code") == 200) {
            Map<String, String> data = (Map<String, String>) result.get("data");
            return data.get("token");
        }
    }
    
    throw new RuntimeException("企业微信登录失败");
}
```

## 注意事项

1. **安全性**: 
   - 企业微信密钥等敏感信息应妥善保管
   - 回调接口会验证签名，确保消息来源可信
   - 建议使用HTTPS协议

2. **回调URL配置**:
   - 回调URL必须是公网可访问的地址
   - 需要在企业微信管理后台配置回调URL
   - 回调接口支持GET（URL验证）和POST（事件推送）

3. **Token管理**:
   - access_token有效期为2小时，系统会自动缓存和刷新
   - suite_ticket每10分钟推送一次，有效期30分钟

4. **用户绑定**:
   - 支持企业微信用户与本地用户的绑定/解绑
   - 可配置是否自动创建用户
   - 支持用户信息同步

5. **日志记录**:
   - 所有回调事件都会记录日志
   - 支持按消息类型、事件类型等条件查询日志
   - 定期清理过期日志
