# 企业微信第三方应用部署和配置指南

## 概述

本指南详细说明如何部署和配置企业微信第三方应用模块，包括环境准备、数据库配置、应用配置等步骤。

## 环境要求

### 系统要求
- **Java**: JDK 1.8 或更高版本
- **数据库**: MySQL 5.7 或更高版本
- **缓存**: Redis 3.0 或更高版本
- **Web服务器**: Tomcat 8.5 或更高版本

### 网络要求
- 服务器需要能够访问企业微信API（qyapi.weixin.qq.com）
- 回调URL需要公网可访问
- 建议使用HTTPS协议

## 数据库配置

### 1. 执行数据库脚本

```sql
-- 执行企业微信相关表的创建脚本
source sql/wework_tables.sql;
```

### 2. 验证表结构

确认以下表已成功创建：
- `wework_config` - 企业微信配置表
- `wework_callback_log` - 回调日志表
- `wework_user_sync` - 用户同步记录表
- `wework_access_token` - 访问令牌缓存表

确认 `sys_user` 表已添加企业微信相关字段：
- `wework_userid` - 企业微信用户ID
- `wework_mobile` - 企业微信手机号
- `wework_avatar` - 企业微信头像
- `wework_name` - 企业微信姓名
- `wework_department` - 企业微信部门
- `wework_bind_time` - 企业微信绑定时间

## 应用配置

### 1. 基础配置

在 `application.yml` 中添加企业微信配置：

```yaml
# 企业微信配置
wework:
  # 企业ID（必填）
  corp-id: ${WEWORK_CORP_ID:your_corp_id}
  # 应用密钥（必填）
  corp-secret: ${WEWORK_CORP_SECRET:your_corp_secret}
  # 应用ID（必填）
  agent-id: ${WEWORK_AGENT_ID:your_agent_id}
  
  # 回调配置
  callback:
    # 回调Token（必填）
    token: ${WEWORK_CALLBACK_TOKEN:your_callback_token}
    # 回调加密密钥（必填）
    encoding-aes-key: ${WEWORK_CALLBACK_AES_KEY:your_callback_aes_key}
    # 数据回调URL
    data-url: /wework/callback/data
    # 指令回调URL
    command-url: /wework/callback/command
  
  # OAuth2配置
  oauth2:
    # 授权回调地址（必填）
    redirect-uri: ${WEWORK_REDIRECT_URI:http://your-domain.com/wework/auth/callback}
    # 授权scope
    scope: snsapi_base
    # 登录开关
    enabled: ${WEWORK_LOGIN_ENABLED:true}
  
  # API配置
  api:
    # API基础URL
    base-url: https://qyapi.weixin.qq.com
    # 连接超时时间（毫秒）
    connect-timeout: 5000
    # 读取超时时间（毫秒）
    read-timeout: 10000
    # 重试次数
    retry-count: 3
```

### 2. 环境变量配置

建议使用环境变量配置敏感信息：

```bash
# 企业微信基础配置
export WEWORK_CORP_ID="your_corp_id"
export WEWORK_CORP_SECRET="your_corp_secret"
export WEWORK_AGENT_ID="your_agent_id"

# 回调配置
export WEWORK_CALLBACK_TOKEN="your_callback_token"
export WEWORK_CALLBACK_AES_KEY="your_callback_aes_key"

# OAuth2配置
export WEWORK_REDIRECT_URI="https://your-domain.com/wework/auth/callback"
export WEWORK_LOGIN_ENABLED="true"
```

### 3. 安全配置

确保以下安全配置已正确设置：

```yaml
# Spring Security配置
security:
  # 企业微信接口允许匿名访问
  permit-all:
    - /wework/auth/**
    - /wework/callback/**

# XSS过滤器配置
xss:
  # 排除企业微信回调接口
  excludes: /system/notice,/wework/callback/*
```

## 企业微信后台配置

### 1. 创建第三方应用

1. 登录企业微信服务商后台
2. 创建第三方应用
3. 获取以下信息：
   - SuiteID（第三方应用ID）
   - SuiteSecret（第三方应用密钥）
   - Token（回调Token）
   - EncodingAESKey（回调加密密钥）

### 2. 配置回调URL

在企业微信后台配置以下回调URL：

- **数据回调URL**: `https://your-domain.com/wework/callback/data`
- **指令回调URL**: `https://your-domain.com/wework/callback/command`

### 3. 配置可信域名

在企业微信后台配置OAuth2授权回调域名：
- `your-domain.com`

### 4. 设置应用权限

根据需要设置应用权限：
- 通讯录权限：读取成员信息
- 消息权限：发送消息
- 其他业务权限

## 部署步骤

### 1. 编译打包

```bash
# 编译整个项目
mvn clean compile

# 打包项目
mvn clean package -Dmaven.test.skip=true
```

### 2. 部署应用

```bash
# 复制WAR包到Tomcat
cp ems-admin/target/ems-admin.war $TOMCAT_HOME/webapps/

# 启动Tomcat
$TOMCAT_HOME/bin/startup.sh
```

### 3. 验证部署

1. 检查应用启动日志
2. 访问健康检查接口：`GET /actuator/health`
3. 测试企业微信配置接口：`GET /wework/auth/status`

## 配置验证

### 1. 回调URL验证

企业微信会向回调URL发送GET请求进行验证：

```bash
# 测试数据回调URL验证
curl "https://your-domain.com/wework/callback/data?msg_signature=xxx&timestamp=xxx&nonce=xxx&echostr=xxx"

# 测试指令回调URL验证
curl "https://your-domain.com/wework/callback/command?msg_signature=xxx&timestamp=xxx&nonce=xxx&echostr=xxx"
```

### 2. 连接测试

使用配置管理接口测试企业微信连接：

```bash
curl -X POST "https://your-domain.com/wework/config/test" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer your_token" \
  -d '{
    "corpId": "your_corp_id",
    "corpSecret": "your_corp_secret",
    "agentId": "your_agent_id"
  }'
```

## 监控和日志

### 1. 日志配置

在 `logback-spring.xml` 中添加企业微信相关日志配置：

```xml
<!-- 企业微信模块日志 -->
<logger name="com.ems.wework" level="INFO" additivity="false">
    <appender-ref ref="wework_file"/>
    <appender-ref ref="console"/>
</logger>

<appender name="wework_file" class="ch.qos.logback.core.rolling.RollingFileAppender">
    <file>logs/wework.log</file>
    <rollingPolicy class="ch.qos.logback.core.rolling.TimeBasedRollingPolicy">
        <fileNamePattern>logs/wework.%d{yyyy-MM-dd}.%i.log</fileNamePattern>
        <timeBasedFileNamingAndTriggeringPolicy class="ch.qos.logback.core.rolling.SizeAndTimeBasedFNATP">
            <maxFileSize>100MB</maxFileSize>
        </timeBasedFileNamingAndTriggeringPolicy>
        <maxHistory>15</maxHistory>
    </rollingPolicy>
    <encoder>
        <pattern>%d{yyyy-MM-dd HH:mm:ss.SSS} [%thread] %-5level %logger{50} - %msg%n</pattern>
    </encoder>
</appender>
```

### 2. 监控指标

建议监控以下指标：
- 回调接口响应时间
- 回调处理成功率
- access_token获取成功率
- 用户登录成功率

### 3. 告警配置

配置以下告警规则：
- 回调接口连续失败
- access_token获取失败
- 用户登录失败率过高

## 故障排除

### 1. 常见问题

**问题1**: 回调URL验证失败
- 检查回调URL是否公网可访问
- 检查Token和EncodingAESKey配置是否正确
- 检查签名验证逻辑

**问题2**: 用户登录失败
- 检查企业ID和应用密钥是否正确
- 检查用户是否在企业微信中激活
- 检查应用权限配置

**问题3**: 消息推送失败
- 检查access_token是否有效
- 检查用户是否关注应用
- 检查消息格式是否正确

### 2. 调试方法

1. 开启DEBUG日志级别
2. 使用企业微信接口调试工具
3. 检查回调日志表中的错误信息
4. 使用Postman等工具测试API接口

### 3. 性能优化

1. 启用Redis缓存access_token
2. 配置连接池优化HTTP请求
3. 异步处理回调事件
4. 定期清理过期日志

## 安全建议

1. **敏感信息保护**：
   - 使用环境变量存储密钥
   - 定期轮换密钥
   - 限制配置文件访问权限

2. **网络安全**：
   - 使用HTTPS协议
   - 配置防火墙规则
   - 启用IP白名单

3. **应用安全**：
   - 验证回调消息签名
   - 限制API调用频率
   - 记录安全审计日志

4. **数据安全**：
   - 加密存储敏感数据
   - 定期备份数据库
   - 控制数据访问权限

## 维护建议

1. **定期维护**：
   - 清理过期日志和缓存
   - 更新依赖库版本
   - 检查配置有效性

2. **监控告警**：
   - 设置关键指标监控
   - 配置异常告警通知
   - 定期检查系统状态

3. **文档更新**：
   - 及时更新配置文档
   - 记录变更历史
   - 维护故障处理手册
