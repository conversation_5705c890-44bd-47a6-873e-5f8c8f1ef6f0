# 项目相关配置
ruoyi:
  # 名称
  name: RuoYi
  # 版本
  version: 3.7.0
  # 版权年份
  copyrightYear: 2021
  # 实例演示开关
  demoEnabled: true
  # 文件路径 示例（ Windows配置D:/ruoyi/uploadPath，Linux配置 /home/<USER>/uploadPath）
  profile: ${LOG_FILE_PATH:/Users/<USER>/ruoyi}
  # 获取ip地址开关
  addressEnabled: false
  # 类型 math 数组计算 char 字符验证
  captchaType: math

# 开发环境配置
server:
  # 服务器的HTTP端口，默认为8080
  port: 8080
  servlet:
    # 应用的访问路径
    context-path: /
  tomcat:
    # tomcat的URI编码
    uri-encoding: UTF-8
    # tomcat最大线程数，默认为200
    max-threads: 800
    # Tomcat启动初始化的线程数，默认值25
    min-spare-threads: 30

# 日志配置
logging:
  level:
    com.ems: debug
    org.springframework: warn

# Spring配置
spring:
  # 资源信息
  messages:
    # 国际化资源文件路径
    basename: i18n/messages
  profiles:
    active: druid
  # 文件上传
  servlet:
     multipart:
       # 单个文件大小
       max-file-size:  10MB
       # 设置总上传的文件大小
       max-request-size:  20MB
  # 服务模块
  devtools:
    restart:
      # 热部署开关
      enabled: true
  # redis 配置
  redis:
    # 地址
    host: ${REDIS_HOST:*************}
    # 端口，默认为6379
    port: ${REDIS_PORT:6379}
    # 数据库索引
    database: 0
    # 密码
    password:
    # 连接超时时间
    timeout: 10s
    lettuce:
      pool:
        # 连接池中的最小空闲连接
        min-idle: 0
        # 连接池中的最大空闲连接
        max-idle: 8
        # 连接池的最大数据库连接数
        max-active: 8
        # #连接池最大阻塞等待时间（使用负值表示没有限制）
        max-wait: -1ms
  influx:
    url: ${INFLUX_URL:http://***********:8086}
    token: ${INFLUX_TOKEN:3ZbGW-OXZV0AFFt_wIupGj9ZikRy2C6JcpJLMUVgdHUbNLyNLUU4f8-sQGwZInUlLRja03jzqpUKYRjEA8pOcg==}   #可以从页面上获取
    org: ${INFLUX_ORG:orange}
    bucket: ${INFLUX_BUCKET:DemoEMS}
  # kafka 配置
#  kafka:
#    bootstrap-servers: ${KAFKA_URL:SJDD-test001:9092}
#    producer:
#      retries: 0
#      # 每次批量发送消息的数量
#      batch-size: 16384
#      buffer-memory: 33554432
#      # 指定消息key和消息体的编解码方式
#      key-serializer: org.apache.kafka.common.serialization.StringSerializer
#      value-serializer: org.apache.kafka.common.serialization.StringSerializer
#    consumer:
#      # 指定默认消费者group id
#      auto-offset-reset: earliest
#      enable-auto-commit: true
#      auto-commit-interval: 100
#      # 指定消息key和消息体的编解码方式
#      key-deserializer: org.apache.kafka.common.serialization.StringDeserializer
#      value-deserializer: org.apache.kafka.common.serialization.StringDeserializer
#      group-id: ${KAFKA_GROUP:ny-test}
# token配置
token:
    # 令牌自定义标识
    header: Authorization
    # 令牌密钥
    secret: abcdefghijklmnopqrstuvwxyz
    # 令牌有效期（默认30分钟）
    expireTime: 30

# MyBatis配置
mybatis-plus:
    # 搜索指定包别名
    typeAliasesPackage: com.ems.**
    # 配置mapper的扫描，找到所有的mapper.xml映射文件
    mapperLocations: classpath*:mapper/**/*Mapper.xml
    # 加载全局的配置文件
    configLocation: classpath:mybatis/mybatis-config.xml
    global-config:
      db-config:
        logic-delete-field: delFlag # 全局逻辑删除的实体字段名
        logic-delete-value: 0      # 逻辑已删除值(默认为0)
        logic-not-delete-value: 1  # 逻辑未删除值(默认为 1)
# PageHelper分页插件
pagehelper:
  helperDialect: mysql
  supportMethodsArguments: true
  params: count=countSql

# Swagger配置
swagger:
  # 是否开启swagger
  enabled: true
  # 请求前缀
  pathMapping: /dev-api

# 防止XSS攻击
xss:
  # 过滤开关
  enabled: true
  # 排除链接（多个用逗号分隔）
  excludes: /system/notice,/wework/callback/*
  # 匹配链接
  urlPatterns: /system/*,/monitor/*,/tool/*
ems-kafka:
  topicName: admin-test

# EMS脚本查询配置
ems:
  script:
    query:
      # 查询超时时间（秒）
      query-timeout: 30
      # 最大结果数量
      max-result-size: 1000
      # 是否启用脚本缓存
      enable-cache: true
      # 缓存过期时间（分钟）
      cache-expire-minutes: 10
      # 是否启用权限验证
      enable-permission-check: true
      # 允许的最大参数数量
      max-parameter-count: 50

# 企业微信配置
wework:
  # 应用类型：self_built(自建应用) 或 third_party(第三方应用)
  app-type: ${WEWORK_APP_TYPE:third_party}
  # 企业ID（自建应用必填，第三方应用可选）
  corp-id: ${WEWORK_CORP_ID:your_corp_id}
  # 应用密钥（自建应用必填）
  corp-secret: ${WEWORK_CORP_SECRET:your_corp_secret}
  # 应用ID（自建应用必填）
  agent-id: ${WEWORK_AGENT_ID:your_agent_id}
  # 第三方应用ID（第三方应用必填）
  suite-id: ${WEWORK_SUITE_ID:ww30dfbab30fe9302d}
  # 第三方应用密钥（第三方应用必填）
  suite-secret: ${WEWORK_SUITE_SECRET:I_CrZoSUunLdIrEsJ4-6AXBwuhA5WkjHS6fhalYlzWQ}
  # 回调配置
  callback:
    # 回调Token
    token: ${WEWORK_CALLBACK_TOKEN:qVtXBkwhiHT2VZZnMjS0fGS0eQsD5T}
    # 回调加密密钥
    encoding-aes-key: ${WEWORK_CALLBACK_AES_KEY:AstdsxvPuleKjJBnXDBIYiaZXSxFJFxujKtHd81o3A7}
    # 数据回调URL
    data-url: /wework/callback/data
    # 指令回调URL
    command-url: /wework/callback/command
  # OAuth2配置
  oauth2:
    # 授权回调地址
    redirect-uri: ${WEWORK_REDIRECT_URI:http://localhost:8080/wework/auth/callback}
    # 授权scope
    scope: snsapi_base
    # 登录开关
    enabled: ${WEWORK_LOGIN_ENABLED:true}
  # API配置
  api:
    # API基础URL
    base-url: https://qyapi.weixin.qq.com
    # 连接超时时间（毫秒）
    connect-timeout: 5000
    # 读取超时时间（毫秒）
    read-timeout: 10000
    # 重试次数
    retry-count: 3
