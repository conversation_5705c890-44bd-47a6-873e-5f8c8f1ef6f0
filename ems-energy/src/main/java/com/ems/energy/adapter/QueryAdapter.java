package com.ems.energy.adapter;

import java.util.List;
import java.util.Map;

/**
 * 查询适配器接口
 * 为不同数据源提供统一的查询接口
 *
 * <AUTHOR>
 * @date 2025-07-29
 */
public interface QueryAdapter {

    /**
     * 执行查询
     *
     * @param script 脚本模板
     * @param params 参数映射
     * @return 查询结果列表
     * @throws Exception 查询异常
     */
    List<Map<String, Object>> executeQuery(String script, Map<String, Object> params) throws Exception;

    /**
     * 执行查询（带结果数量限制）
     *
     * @param script 脚本模板
     * @param params 参数映射
     * @param maxResultSize 最大结果数量限制
     * @return 查询结果列表
     * @throws Exception 查询异常
     */
    List<Map<String, Object>> executeQuery(String script, Map<String, Object> params, int maxResultSize) throws Exception;

    /**
     * 获取支持的数据库类型
     *
     * @return 数据库类型
     */
    String getSupportedDbType();

    /**
     * 验证脚本安全性
     *
     * @param script 脚本内容
     * @return 是否安全
     */
    boolean validateScript(String script);

    /**
     * 处理参数替换
     *
     * @param script 原始脚本
     * @param params 参数映射
     * @return 处理后的脚本
     */
    String processParameters(String script, Map<String, Object> params);
}
