package com.ems.energy.controller;

import java.util.List;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import org.springframework.security.access.prepost.PreAuthorize;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import com.ems.common.annotation.Log;
import com.ems.common.core.controller.BaseController;
import com.ems.common.core.domain.AjaxResult;
import com.ems.common.enums.BusinessType;
import com.ems.energy.domain.EmsEquipment;
import com.ems.energy.service.IEmsEquipmentService;
import com.ems.common.utils.poi.ExcelUtil;

/**
 * 设备管理Controller
 *
 * <AUTHOR>
 * @date 2022-03-08
 */
@Api("设备管理")
@RestController
@RequestMapping("/energy/equipment")
public class EmsEquipmentController extends BaseController
{
    @Autowired
    private IEmsEquipmentService emsEquipmentService;

    /**
     * 查询设备管理列表
     */
    @ApiOperation("查询设备管理列表")
    @PreAuthorize("@ss.hasPermi('energy:equipment:list')")
    @GetMapping("/list")
    public AjaxResult list(EmsEquipment emsEquipment)
    {
        List<EmsEquipment> list = emsEquipmentService.selectEmsEquipmentList(emsEquipment);
        return AjaxResult.success(list);
    }

    /**
     * 导出设备管理列表
     */
    @ApiOperation("导出设备管理列表")
    @PreAuthorize("@ss.hasPermi('energy:equipment:export')")
    @Log(title = "设备管理", businessType = BusinessType.EXPORT)
    @PostMapping("/export")
    public AjaxResult export(EmsEquipment emsEquipment)
    {
        List<EmsEquipment> list = emsEquipmentService.selectEmsEquipmentList(emsEquipment);
        ExcelUtil<EmsEquipment> util = new ExcelUtil<>(EmsEquipment.class);
        return util.exportExcel(list, "设备管理数据");
    }

    /**
     * 获取设备管理详细信息
     */
    @ApiOperation("获取设备管理详细信息")
    @PreAuthorize("@ss.hasPermi('energy:equipment:query')")
    @GetMapping(value = "/{equipmentId}")
    public AjaxResult getInfo(@PathVariable("equipmentId") Long equipmentId)
    {
        return AjaxResult.success(emsEquipmentService.selectEmsEquipmentByEquipmentId(equipmentId));
    }

    /**
     * 新增设备管理
     */
    @ApiOperation("新增设备管理")
    @PreAuthorize("@ss.hasPermi('energy:equipment:add')")
    @Log(title = "设备管理", businessType = BusinessType.INSERT)
    @PostMapping
    public AjaxResult add(@RequestBody EmsEquipment emsEquipment)
    {
        return toAjax(emsEquipmentService.insertEmsEquipment(emsEquipment));
    }

    /**
     * 修改设备管理
     */
    @ApiOperation("修改设备管理")
    @PreAuthorize("@ss.hasPermi('energy:equipment:edit')")
    @Log(title = "设备管理", businessType = BusinessType.UPDATE)
    @PutMapping
    public AjaxResult edit(@RequestBody EmsEquipment emsEquipment)
    {
        return toAjax(emsEquipmentService.updateEmsEquipment(emsEquipment));
    }

    /**
     * 删除设备管理
     */
    @ApiOperation("删除设备管理")
    @PreAuthorize("@ss.hasPermi('energy:equipment:remove')")
    @Log(title = "设备管理", businessType = BusinessType.DELETE)
	@DeleteMapping("/{equipmentIds}")
    public AjaxResult remove(@PathVariable Long[] equipmentIds)
    {
        return toAjax(emsEquipmentService.deleteEmsEquipmentByEquipmentIds(equipmentIds));
    }
}
