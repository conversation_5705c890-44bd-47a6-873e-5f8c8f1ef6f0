package com.ems.energy.domain.dto;

import io.swagger.annotations.ApiModel;
import io.swagger.annotations.ApiModelProperty;

import java.util.List;
import java.util.Map;

/**
 * 脚本查询响应DTO
 *
 * <AUTHOR>
 * @date 2025-07-29
 */
@ApiModel("脚本查询响应")
public class ScriptQueryResponse {

    @ApiModelProperty("执行状态")
    private boolean success;

    @ApiModelProperty("执行信息")
    private String message;

    @ApiModelProperty("查询结果")
    private List<Map<String, Object>> data;

    @ApiModelProperty("结果总数")
    private int total;

    public boolean isSuccess() {
        return success;
    }

    public void setSuccess(boolean success) {
        this.success = success;
    }

    public String getMessage() {
        return message;
    }

    public void setMessage(String message) {
        this.message = message;
    }

    public List<Map<String, Object>> getData() {
        return data;
    }

    public void setData(List<Map<String, Object>> data) {
        this.data = data;
    }

    public int getTotal() {
        return total;
    }

    public void setTotal(int total) {
        this.total = total;
    }

    @Override
    public String toString() {
        return "ScriptQueryResponse{" +
                "success=" + success +
                ", message='" + message + '\'' +
                ", data=" + data +
                ", total=" + total +
                '}';
    }
}
