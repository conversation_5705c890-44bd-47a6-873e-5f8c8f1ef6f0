package com.ems.energy.domain;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableLogic;
import com.baomidou.mybatisplus.annotation.TableName;
import com.ems.common.annotation.Excel;
import com.ems.common.core.domain.BaseEntity;
import com.fasterxml.jackson.annotation.JsonFormat;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import java.util.Date;

/**
 * 脚本配置对象 script_config
 *
 * <AUTHOR>
 * @date 2025-07-29
 */
@TableName("script_config")
public class ScriptConfig extends BaseEntity {
    private static final long serialVersionUID = 1L;

    /** 主键ID */
    @TableId(type = IdType.AUTO)
    private Long id;

    /** 脚本代码标识符 */
    @Excel(name = "脚本代码标识符")
    @NotBlank(message = "脚本代码标识符不能为空")
    @Size(max = 100, message = "脚本代码标识符长度不能超过100个字符")
    private String code;

    /** 实际执行的SQL/InfluxQL脚本模板 */
    @Excel(name = "脚本模板")
    @NotBlank(message = "脚本模板不能为空")
    private String script;

    /** 脚本描述信息 */
    @Excel(name = "脚本描述")
    @Size(max = 500, message = "脚本描述长度不能超过500个字符")
    private String description;

    /** 数据库类型（MYSQL/INFLUXDB） */
    @Excel(name = "数据库类型")
    @NotBlank(message = "数据库类型不能为空")
    private String dbType;

    /** 创建时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField("created_time")
    private Date createdTime;

    /** 更新时间 */
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField("updated_time")
    private Date updatedTime;

    /** 删除标志（0代表删除 1代表存在） */
    @TableLogic
    private String delFlag;

    public Long getId() {
        return id;
    }

    public void setId(Long id) {
        this.id = id;
    }

    public String getCode() {
        return code;
    }

    public void setCode(String code) {
        this.code = code;
    }

    public String getScript() {
        return script;
    }

    public void setScript(String script) {
        this.script = script;
    }

    public String getDescription() {
        return description;
    }

    public void setDescription(String description) {
        this.description = description;
    }

    public String getDbType() {
        return dbType;
    }

    public void setDbType(String dbType) {
        this.dbType = dbType;
    }

    public Date getCreatedTime() {
        return createdTime;
    }

    public void setCreatedTime(Date createdTime) {
        this.createdTime = createdTime;
    }

    public Date getUpdatedTime() {
        return updatedTime;
    }

    public void setUpdatedTime(Date updatedTime) {
        this.updatedTime = updatedTime;
    }

    public String getDelFlag() {
        return delFlag;
    }

    public void setDelFlag(String delFlag) {
        this.delFlag = delFlag;
    }

    @Override
    public String toString() {
        return "ScriptConfig{" +
                "id=" + id +
                ", code='" + code + '\'' +
                ", script='" + script + '\'' +
                ", description='" + description + '\'' +
                ", dbType='" + dbType + '\'' +
                ", createdTime=" + createdTime +
                ", updatedTime=" + updatedTime +
                ", delFlag='" + delFlag + '\'' +
                '}';
    }
}
