package com.ems.energy.utils;

import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;
import java.util.regex.Pattern;

/**
 * SQL安全工具类
 * 提供SQL注入防护和脚本安全验证
 *
 * <AUTHOR>
 * @date 2025-07-29
 */
@Component
public class SqlSecurityUtils {

    // 危险的DDL关键字（不允许执行）
    private static final List<String> DANGEROUS_DDL_KEYWORDS = Arrays.asList(
            "DROP", "CREATE", "ALTER", "TRUNCATE", "RENAME",
            "EXEC", "EXECUTE", "SCRIPT", "JAVASCRIPT", "VBSCRIPT",
            "ONLOAD", "ONERROR", "ONCLICK", "EVAL", "EXPRESSION"
    );

    // 允许的DML关键字
    private static final List<String> ALLOWED_DML_KEYWORDS = Arrays.asList(
            "SELECT", "INSERT", "UPDATE", "DELETE"
    );

    // 危险的Flux关键字
    private static final List<String> DANGEROUS_FLUX_KEYWORDS = Arrays.asList(
            "import", "option", "builtin"
    );

    // SQL注释模式
    private static final Pattern SQL_COMMENT_PATTERN = Pattern.compile("(--[^\r\n]*)|(/\\*[\\w\\W]*?(?=\\*/)\\*/)");

    // 多语句模式
    private static final Pattern MULTI_STATEMENT_PATTERN = Pattern.compile(";\\s*\\w");

    /**
     * 验证SQL脚本安全性
     *
     * @param sql SQL脚本
     * @return 是否安全
     */
    public boolean validateSql(String sql) {
        if (sql == null || sql.trim().isEmpty()) {
            return false;
        }

        String upperSql = sql.toUpperCase().trim();

        // 检查是否以允许的DML关键字开头
        boolean startsWithAllowedKeyword = false;
        for (String keyword : ALLOWED_DML_KEYWORDS) {
            if (upperSql.startsWith(keyword)) {
                startsWithAllowedKeyword = true;
                break;
            }
        }

        if (!startsWithAllowedKeyword) {
            return false;
        }

        // 检查危险的DDL关键字（使用单词边界匹配，避免误判字段名）
        for (String keyword : DANGEROUS_DDL_KEYWORDS) {
            // 使用正则表达式匹配完整单词，避免误判字段名如create_time
            // \b表示单词边界，确保只匹配独立的关键字
            String pattern = "\\b" + keyword + "\\b";
            if (Pattern.compile(pattern).matcher(upperSql).find()) {
                return false;
            }
        }

        // 检查SQL注释
        if (SQL_COMMENT_PATTERN.matcher(sql).find()) {
            return false;
        }

        // 检查多语句
        if (MULTI_STATEMENT_PATTERN.matcher(sql).find()) {
            return false;
        }

        // 检查特殊字符
        if (containsDangerousCharacters(sql)) {
            return false;
        }

        return true;
    }

    /**
     * 验证Flux查询安全性
     *
     * @param flux Flux查询脚本
     * @return 是否安全
     */
    public boolean validateFluxQuery(String flux) {
        if (flux == null || flux.trim().isEmpty()) {
            return false;
        }

        String lowerFlux = flux.toLowerCase();

        // 检查危险的Flux关键字
        for (String keyword : DANGEROUS_FLUX_KEYWORDS) {
            if (lowerFlux.contains(keyword.toLowerCase())) {
                return false;
            }
        }

        // 检查是否只包含查询操作
        if (!lowerFlux.trim().startsWith("from(")) {
            return false;
        }

        // 检查特殊字符
        if (containsDangerousCharacters(flux)) {
            return false;
        }

        return true;
    }

    /**
     * 检查是否包含危险字符
     *
     * @param input 输入字符串
     * @return 是否包含危险字符
     */
    private boolean containsDangerousCharacters(String input) {
        // 检查脚本标签
        if (input.toLowerCase().contains("<script")) {
            return true;
        }

        // 检查危险的特殊字符组合
        String[] dangerousPatterns = {
                "xp_", "sp_", "0x", "char(", "ascii(", "substring(",
                "waitfor", "delay", "benchmark(", "sleep("
        };

        String lowerInput = input.toLowerCase();
        for (String pattern : dangerousPatterns) {
            if (lowerInput.contains(pattern)) {
                return true;
            }
        }

        return false;
    }

    /**
     * 清理和转义输入字符串
     *
     * @param input 输入字符串
     * @return 清理后的字符串
     */
    public String sanitizeInput(String input) {
        if (input == null) {
            return null;
        }

        return input.replaceAll("[<>\"'%;()&+]", "");
    }

    /**
     * 验证参数名称是否安全
     *
     * @param paramName 参数名称
     * @return 是否安全
     */
    public boolean validateParameterName(String paramName) {
        if (paramName == null || paramName.trim().isEmpty()) {
            return false;
        }

        // 参数名只能包含字母、数字和下划线
        return paramName.matches("^[a-zA-Z_][a-zA-Z0-9_]*$");
    }
}
