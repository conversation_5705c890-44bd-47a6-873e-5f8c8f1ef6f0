package com.ems.energy.utils;

import com.ems.common.utils.DateUtils;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.Date;
import java.util.List;
import java.util.Map;
import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 参数处理器
 * 处理脚本中的参数占位符替换
 *
 * <AUTHOR>
 * @date 2025-07-29
 */
@Component
public class ParameterProcessor {

    private static final Pattern PARAM_PATTERN = Pattern.compile("#\\{([^}]+)\\}");
    private static final SimpleDateFormat DATE_FORMAT = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
    private static final SimpleDateFormat INFLUX_DATE_FORMAT = new SimpleDateFormat("yyyy-MM-dd'T'HH:mm:ss'Z'");

    /**
     * 处理参数替换（支持指定数据库类型）
     *
     * @param script 原始脚本
     * @param params 参数映射
     * @param dbType 数据库类型
     * @return 处理后的脚本
     */
    public String processParameters(String script, Map<String, Object> params, String dbType) {
        if (script == null || params == null) {
            return script;
        }

        Matcher matcher = PARAM_PATTERN.matcher(script);
        StringBuffer result = new StringBuffer();

        while (matcher.find()) {
            String paramName = matcher.group(1);
            Object paramValue = params.get(paramName);

            String replacement = formatParameterValue(paramValue, dbType);
            matcher.appendReplacement(result, Matcher.quoteReplacement(replacement));
        }

        matcher.appendTail(result);
        return result.toString();
    }

    /**
     * 格式化参数值（支持数据库类型）
     *
     * @param value 参数值
     * @param dbType 数据库类型
     * @return 格式化后的字符串
     */
    private String formatParameterValue(Object value, String dbType) {
        if (value == null) {
            return "NULL";
        }

        boolean isInfluxDB = "INFLUXDB".equals(dbType);

        // 字符串类型：InfluxDB不需要单引号，MySQL需要
        if (value instanceof String) {
            String stringValue = (String) value;
            if (isInfluxDB) {
                // InfluxDB字符串参数处理
                if (isTimeString(stringValue)) {
                    return stringValue; // 时间字符串直接返回，不加引号
                } else if (isDurationString(stringValue)) {
                    return stringValue; // duration字符串直接返回，不加引号
                } else {
                    return "\"" + stringValue + "\""; // 普通字符串用双引号
                }
            } else {
                return "'" + escapeString(stringValue) + "'";
            }
        }

        // 数值类型：直接转换
        if (value instanceof Number) {
            return value.toString();
        }

        // 布尔类型：转换为数据库格式
        if (value instanceof Boolean) {
            return ((Boolean) value) ? "1" : "0";
        }

        // 日期类型：根据数据库类型选择格式
        if (value instanceof Date) {
            if (isInfluxDB) {
                // InfluxDB使用ISO格式，不需要引号
                return INFLUX_DATE_FORMAT.format((Date) value);
            } else {
                // MySQL使用标准格式，需要引号
                return "'" + DATE_FORMAT.format((Date) value) + "'";
            }
        }

        // 列表类型：转换为IN子句格式
        if (value instanceof List) {
            return formatListValue((List<?>) value, dbType);
        }

        // 其他类型：根据数据库类型决定是否加引号
        if (isInfluxDB) {
            return value.toString();
        } else {
            return "'" + escapeString(value.toString()) + "'";
        }
    }

    /**
     * 格式化列表值为IN子句格式（支持数据库类型）
     *
     * @param list 列表值
     * @param dbType 数据库类型
     * @return IN子句格式字符串
     */
    private String formatListValue(List<?> list, String dbType) {
        if (list == null || list.isEmpty()) {
            return "('')";
        }

        boolean isInfluxDB = "INFLUXDB".equals(dbType);
        StringBuilder sb = new StringBuilder();
        if (!isInfluxDB) {
            sb.append("(");
        }

        for (int i = 0; i < list.size(); i++) {
            if (i > 0) {
                sb.append(", ");
            }
            Object item = list.get(i);
            if (item instanceof String) {
                if (isInfluxDB) {
                    sb.append("\"").append(escapeString((String) item)).append("\"");
                } else {
                    sb.append("'").append(escapeString((String) item)).append("'");
                }
            } else if (item instanceof Number) {
                sb.append(item.toString());
            } else {
                if (isInfluxDB) {
                    sb.append(item.toString());
                } else {
                    sb.append("'").append(escapeString(item.toString())).append("'");
                }
            }
        }
        if (!isInfluxDB) {
            sb.append(")");
        }
        return sb.toString();
    }

    /**
     * 转义字符串中的特殊字符
     *
     * @param str 原始字符串
     * @return 转义后的字符串
     */
    private String escapeString(String str) {
        if (str == null) {
            return "";
        }
        return str.replace("'", "''")
                  .replace("\\", "\\\\");
    }

    /**
     * 格式化InfluxDB时间参数
     *
     * @param date 日期对象
     * @return InfluxDB时间格式字符串
     */
    public String formatInfluxTime(Date date) {
        if (date == null) {
            return "now()";
        }
        return INFLUX_DATE_FORMAT.format(date);
    }

    /**
     * 检测字符串是否为时间格式
     *
     * @param str 字符串
     * @return 是否为时间格式
     */
    private boolean isTimeString(String str) {
        if (str == null) {
            return false;
        }
        // 检测常见的时间格式
        return str.matches("\\d{4}-\\d{2}-\\d{2}T\\d{2}:\\d{2}:\\d{2}.*") || // ISO格式
               str.matches("\\d{4}-\\d{2}-\\d{2} \\d{2}:\\d{2}:\\d{2}") ||   // 标准格式
               str.equals("now()") ||                                        // InfluxDB now()
               str.matches("-\\d+[smhd]");                                   // 相对时间如-1h
    }

    /**
     * 检测字符串是否为InfluxDB的duration格式
     *
     * @param str 字符串
     * @return 是否为duration格式
     */
    private boolean isDurationString(String str) {
        if (str == null) {
            return false;
        }
        // 检测InfluxDB duration格式：数字+单位(ns,us,ms,s,m,h,d,w,mo,y)
        return str.matches("\\d+(?:ns|us|ms|s|m|h|d|w|mo|y)");
    }
}
