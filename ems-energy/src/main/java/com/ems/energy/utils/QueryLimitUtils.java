package com.ems.energy.utils;

import org.springframework.stereotype.Component;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

/**
 * 查询限制工具类
 * 用于在SQL和Flux查询中添加结果数量限制
 *
 * <AUTHOR>
 * @date 2025-07-29
 */
@Component
public class QueryLimitUtils {

    // MySQL LIMIT 子句匹配模式（不区分大小写）
    private static final Pattern MYSQL_LIMIT_PATTERN = Pattern.compile(
            "\\bLIMIT\\s+(\\d+)(?:\\s*,\\s*(\\d+))?\\s*$", 
            Pattern.CASE_INSENSITIVE
    );

    // InfluxDB limit() 函数匹配模式
    private static final Pattern INFLUX_LIMIT_PATTERN = Pattern.compile(
            "\\|>\\s*limit\\s*\\(\\s*n\\s*:\\s*(\\d+)\\s*\\)", 
            Pattern.CASE_INSENSITIVE
    );

    /**
     * 为MySQL查询添加LIMIT限制
     *
     * @param sql 原始SQL查询
     * @param maxResultSize 最大结果数量
     * @return 添加LIMIT后的SQL
     */
    public String addMySQLLimit(String sql, int maxResultSize) {
        if (sql == null || sql.trim().isEmpty()) {
            return sql;
        }

        String trimmedSql = sql.trim();
        
        // 检查是否已存在LIMIT子句
        Matcher matcher = MYSQL_LIMIT_PATTERN.matcher(trimmedSql);
        if (matcher.find()) {
            // 已存在LIMIT，检查是否需要调整
            String existingLimit = matcher.group(1);
            int currentLimit = Integer.parseInt(existingLimit);
            
            if (currentLimit > maxResultSize) {
                // 现有LIMIT超过最大限制，需要替换
                return matcher.replaceFirst("LIMIT " + maxResultSize);
            } else {
                // 现有LIMIT在允许范围内，保持不变
                return trimmedSql;
            }
        } else {
            // 不存在LIMIT，添加LIMIT子句
            // 移除末尾的分号（如果存在）
            if (trimmedSql.endsWith(";")) {
                trimmedSql = trimmedSql.substring(0, trimmedSql.length() - 1);
            }
            return trimmedSql + " LIMIT " + maxResultSize;
        }
    }

    /**
     * 为InfluxDB查询添加limit限制
     *
     * @param flux 原始Flux查询
     * @param maxResultSize 最大结果数量
     * @return 添加limit后的Flux查询
     */
    public String addInfluxDBLimit(String flux, int maxResultSize) {
        if (flux == null || flux.trim().isEmpty()) {
            return flux;
        }

        String trimmedFlux = flux.trim();
        
        // 检查是否已存在limit()函数
        Matcher matcher = INFLUX_LIMIT_PATTERN.matcher(trimmedFlux);
        if (matcher.find()) {
            // 已存在limit，检查是否需要调整
            String existingLimit = matcher.group(1);
            int currentLimit = Integer.parseInt(existingLimit);
            
            if (currentLimit > maxResultSize) {
                // 现有limit超过最大限制，需要替换
                return matcher.replaceFirst("|> limit(n: " + maxResultSize + ")");
            } else {
                // 现有limit在允许范围内，保持不变
                return trimmedFlux;
            }
        } else {
            // 不存在limit，添加limit函数
            return trimmedFlux + " |> limit(n: " + maxResultSize + ")";
        }
    }

    /**
     * 检查MySQL查询是否已包含LIMIT子句
     *
     * @param sql SQL查询
     * @return 是否包含LIMIT
     */
    public boolean hasMySQLLimit(String sql) {
        if (sql == null || sql.trim().isEmpty()) {
            return false;
        }
        return MYSQL_LIMIT_PATTERN.matcher(sql.trim()).find();
    }

    /**
     * 检查InfluxDB查询是否已包含limit函数
     *
     * @param flux Flux查询
     * @return 是否包含limit
     */
    public boolean hasInfluxDBLimit(String flux) {
        if (flux == null || flux.trim().isEmpty()) {
            return false;
        }
        return INFLUX_LIMIT_PATTERN.matcher(flux.trim()).find();
    }

    /**
     * 从MySQL查询中提取现有的LIMIT值
     *
     * @param sql SQL查询
     * @return LIMIT值，如果不存在则返回-1
     */
    public int extractMySQLLimit(String sql) {
        if (sql == null || sql.trim().isEmpty()) {
            return -1;
        }
        
        Matcher matcher = MYSQL_LIMIT_PATTERN.matcher(sql.trim());
        if (matcher.find()) {
            return Integer.parseInt(matcher.group(1));
        }
        return -1;
    }

    /**
     * 从InfluxDB查询中提取现有的limit值
     *
     * @param flux Flux查询
     * @return limit值，如果不存在则返回-1
     */
    public int extractInfluxDBLimit(String flux) {
        if (flux == null || flux.trim().isEmpty()) {
            return -1;
        }
        
        Matcher matcher = INFLUX_LIMIT_PATTERN.matcher(flux.trim());
        if (matcher.find()) {
            return Integer.parseInt(matcher.group(1));
        }
        return -1;
    }
}
