# 修复最大数量限制控制内存溢出问题

## 问题描述
原有的最大数量限制控制实现存在严重的内存溢出风险：
- 先执行完整查询获取所有结果
- 然后使用 `subList()` 方法截断到指定数量
- 如果查询返回几百万条记录，会先全部加载到内存中，造成内存溢出

## 问题代码位置
`ems-energy/src/main/java/com/ems/energy/service/impl/ScriptQueryServiceImpl.java` 第88-99行：

```java
// 4. 执行查询（带超时控制）
List<Map<String, Object>> data = executeQueryWithTimeout(adapter, scriptConfig.getScript(), params);

// 5. 检查结果数量限制
if (data.size() > scriptQueryProperties.getMaxResultSize()) {
    log.warn("查询结果数量超过限制，code: {}, 结果数量: {}, 限制: {}",
            code, data.size(), scriptQueryProperties.getMaxResultSize());
    data = data.subList(0, scriptQueryProperties.getMaxResultSize());
    response.setMessage("查询成功，结果已截断到" + scriptQueryProperties.getMaxResultSize() + "条");
}
```

## 解决方案
采用数据库层面限制方案，在SQL/Flux查询中直接添加LIMIT/limit子句，从数据库层面就限制返回的记录数。

## 实施内容

### 1. 扩展QueryAdapter接口
- 添加带最大结果数限制的 `executeQuery(String script, Map<String, Object> params, int maxResultSize)` 方法
- 保持向后兼容性

### 2. 创建QueryLimitUtils工具类
- `addMySQLLimit()`: 为MySQL查询添加LIMIT子句
- `addInfluxDBLimit()`: 为InfluxDB查询添加limit()函数
- 智能处理已存在限制的情况
- 支持大小写不敏感匹配

### 3. 修改MySQLQueryAdapter
- 实现新的 `executeQuery()` 重载方法
- 在SQL执行前自动添加LIMIT限制
- 处理已存在LIMIT的情况，确保不超过安全范围

### 4. 修改InfluxDBQueryAdapter
- 实现新的 `executeQuery()` 重载方法
- 在Flux查询中自动添加limit()函数
- 重构为内部统一处理方法

### 5. 更新ScriptQueryServiceImpl
- 修改查询调用逻辑，使用带限制的查询方法
- 移除后置的subList截断逻辑
- 调整日志记录和提示信息

## 修复效果验证

### MySQL查询示例
```sql
-- 原查询
SELECT * FROM ems_equipment WHERE status = 1

-- 修复后自动添加
SELECT * FROM ems_equipment WHERE status = 1 LIMIT 10000

-- 已有LIMIT但超过限制
SELECT * FROM ems_equipment WHERE status = 1 LIMIT 50000
-- 自动调整为
SELECT * FROM ems_equipment WHERE status = 1 LIMIT 10000
```

### InfluxDB查询示例
```flux
// 原查询
from(bucket: "test") |> range(start: -1h) |> filter(fn: (r) => r["device"] == "001")

// 修复后自动添加
from(bucket: "test") |> range(start: -1h) |> filter(fn: (r) => r["device"] == "001") |> limit(n: 10000)

// 已有limit但超过限制
from(bucket: "test") |> range(start: -1h) |> limit(n: 50000)
// 自动调整为
from(bucket: "test") |> range(start: -1h) |> limit(n: 10000)
```

## 核心优势

### 1. 内存安全
- ✅ 从数据库层面限制结果数量
- ✅ 避免大量数据传输到应用层
- ✅ 防止内存溢出风险

### 2. 性能提升
- ✅ 减少网络传输开销
- ✅ 降低内存占用
- ✅ 提高查询响应速度

### 3. 智能处理
- ✅ 自动检测已存在的限制
- ✅ 只在必要时调整限制值
- ✅ 保持查询语义不变

### 4. 兼容性
- ✅ 保持原有接口向后兼容
- ✅ 支持MySQL和InfluxDB两种数据库
- ✅ 不影响现有功能

## 配置说明
最大结果数量限制通过配置文件控制：

```yaml
ems:
  script:
    query:
      max-result-size: 10000  # 最大结果数量，默认10000
```

## 测试验证
- ✅ 创建了完整的单元测试
- ✅ 验证了MySQL和InfluxDB的限制添加逻辑
- ✅ 测试了边界情况和异常输入
- ✅ 确认了修复效果

## 影响范围
- 修改文件：5个
- 新增文件：2个
- 测试文件：1个
- 向后兼容：是
- 破坏性变更：无

## 部署注意事项
1. 此修复无需额外配置，使用现有的 `max-result-size` 配置
2. 修复后查询性能会有显著提升
3. 对于已有LIMIT的查询，会智能保持或调整到安全范围
4. 建议在生产环境部署前进行充分测试
